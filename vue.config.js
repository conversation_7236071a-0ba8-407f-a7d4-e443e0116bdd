"use strict";
const path = require("path");
const SyncApiPlugin = require("./code-analyze/syncapiplugin.js");
var devConfig;
try {
  devConfig = require("./dev-config.js");
} catch (e) {
  console.log(e);
}

if (!devConfig || !devConfig.devServer) {
  console.log(
    "***************未找到dev-config.js，使用默认http://localhost:8081*****************"
  );
} else {
  console.log(
    "***************找到dev-config.js，使用" +
      devConfig.devServer +
      "*****************"
  );
}
function resolve(dir) {
  return path.join(__dirname, dir);
}
const name = "vue Admin Template"; // page title
const port = process.env.port || process.env.npm_config_port || 8080; // dev port
module.exports = {
  // publicPath: '/bank/',
  publicPath: ".",
  outputDir: "dist/ytsqyrzfwpt",
  assetsDir: "static",
  lintOnSave: process.env.NODE_ENV === "development",
  productionSourceMap: false,
  devServer: {
    port: port,
    open: true,
    disableHostCheck: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      "/api/*": {
        target: devConfig
          ? devConfig.devServer
          : null || "http://*********:8183/",
        changeOrigin: true,
        secure: false
        // pathRewrite: {
        //   "^/app-company": ""
        // }
      },
      "/publicFile/*": {
        target: devConfig
          ? devConfig.devServer
          : null || "http://*********:8183/",

        changeOrigin: true,
        secure: false
      },
      "/privateFile/*": {
        target: devConfig
          ? devConfig.devServer
          : null || "http://*********:8183/",

        changeOrigin: true,
        secure: false
      },

      "/pdf_static/*": {
        target: devConfig
          ? devConfig.devServer
          : null || "http://*********:8183/",
        changeOrigin: true,
        secure: false
      }
    }
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        "@": resolve("src")
      }
    }
  },
  chainWebpack(config) {
    config.plugins.delete("preload"); // TODO: need test
    config.plugins.delete("prefetch"); // TODO: need test

    // set svg-sprite-loader
    config.module
      .rule("svg")
      .exclude.add(resolve("src/icons"))
      .end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]"
      })
      .end();

    // set preserveWhitespace
    config.module
      .rule("vue")
      .use("vue-loader")
      .loader("vue-loader")
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true;
        return options;
      })
      .end();

    config
      // https://webpack.js.org/configuration/devtool/#development
      .when(process.env.NODE_ENV === "development", config =>
        config.devtool("eval-source-map")
      );
    config.when(process.env.ENV === "syncapi", config =>
      config.plugin("SyncApiPlugin").use(SyncApiPlugin)
    );
    config.plugin("HtmlWebpackPlugin").use("html-webpack-plugin", [
      {
        template:
          process.env.NODE_ENV !== "development"
            ? "src/indexAsd.html"
            : "src/index.html",
        filename: "index.html"

        // minify: {
        //   removeComments: true,
        //   collapseWhitespace: true,
        //   removeAttributeQuotes: true
        // }
      }
    ]);
    config.plugin("copy-webpack-plugin").use(require("copy-webpack-plugin"), [
      [
        { from: "wrP4ajulOS.txt", to: "" },
        { from: "sm2/", to: "sm2/" }
        // { from: "SM.js", to: "." },
        // { from: "asdConfig.js", to: "" },
        // { from: "应用需求文档.doc", to: "" },
        // { from: "压力测试文档.docx", to: "" },
        // { from: "版本检测报告.xlsx", to: "" }
      ]
    ]);
    // config.when(process.env.NODE_ENV !== "development", config => {
    //   config
    //     .plugin("ScriptExtHtmlWebpackPlugin")
    //     .after("html")
    //     .use("script-ext-html-webpack-plugin", [
    //       {
    //         // `runtime` must same as runtimeChunk name. default is `runtime`
    //         inline: /runtime\..*\.js$/
    //       }
    //     ])
    //     .end();
    //   config.optimization.splitChunks({
    //     chunks: "all",
    //     cacheGroups: {
    //       libs: {
    //         name: "chunk-libs",
    //         test: /[\\/]node_modules[\\/]/,
    //         priority: 10,
    //         chunks: "initial" // only package third parties that are initially dependent
    //       },
    //       elementUI: {
    //         name: "chunk-elementUI", // split elementUI into a single package
    //         priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
    //         test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
    //       },
    //       commons: {
    //         name: "chunk-commons",
    //         test: resolve("src/components"), // can customize your rules
    //         minChunks: 3, //  minimum common number
    //         priority: 5,
    //         reuseExistingChunk: true
    //       }
    //     }
    //   });
    //   config.optimization.runtimeChunk("single");
    // });
  }
};

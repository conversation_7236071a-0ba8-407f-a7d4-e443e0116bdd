<template>
  <div class="enterprise-search">
    <!-- 头部导航 -->
    <van-nav-bar
      title="一分钟知企业"
      left-arrow
      @click-left="onClickLeft"
      class="custom-nav-bar"
    />

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-header">
        <div class="search-content">
          <h2 class="search-title">企业查询</h2>
          <van-search
            v-model="searchValue"
            placeholder="请输入企业名称"
            shape="round"
            background="transparent"
            @search="onSearch"
            class="search-input"
          />
        </div>
        <div class="search-illustration">
          <img
            :src="require('@/assets/images/companyList/header_title.png')"
            alt="企业查询"
          />
        </div>
      </div>
    </div>

    <!-- 搜索分类 -->
    <div class="category-section">
      <h3 class="category-title">搜索分类</h3>
      <div class="category-grid">
        <div
          v-for="(category, index) in categories"
          :key="index"
          class="category-item"
          @click="selectCategory(category)"
        >
          <div
            class="category-icon"
            :style="{ backgroundColor: category.color }"
          >
            <van-icon :name="category.icon" />
          </div>
          <div class="category-name">{{ category.name }}</div>
        </div>
      </div>
    </div>

    <!-- 筛选和排序 -->
    <!-- <div class="filter-section">
      <div class="filter-item" @click="showLocationPicker = true">
        <van-icon name="location-o" />
        <span>{{ selectedLocation }}</span>
      </div>
      <div class="filter-item" @click="showSortPicker = true">
        <van-icon name="sort" />
        <span>{{ selectedSort }}</span>
      </div>
      <div class="filter-item" @click="showMoreFilters = true">
        <van-icon name="filter-o" />
        <span>发现更多</span>
      </div>
    </div> -->

    <!-- 搜索结果 -->
    <div class="results-section">
      <div class="results-header">
        <span>发现 {{ totalCount }} 家企业</span>
      </div>

      <div class="enterprise-list">
        <div
          v-for="(enterprise, index) in enterpriseList"
          :key="index"
          class="enterprise-card"
          @click="viewEnterpriseDetail(enterprise)"
        >
          <!-- 企业名称和图标 -->
          <div class="enterprise-header">
            <div
              class="enterprise-icon"
              :style="{ backgroundColor: enterprise.iconColor }"
            >
              {{ enterprise.name.charAt(0) }}
            </div>
            <h4 class="enterprise-name">{{ enterprise.name }}</h4>
          </div>

          <!-- 企业标签 -->
          <div class="enterprise-tags">
            <span
              v-for="(tag, tagIndex) in enterprise.tags"
              :key="tagIndex"
              class="enterprise-tag"
              :class="getTagClass(tag)"
            >
              {{ tag }}
            </span>
          </div>

          <!-- 企业信息 -->
          <div class="enterprise-info">
            <div class="info-row">
              <div class="info-item">
                <div class="info-label">法定代表人</div>
                <div class="info-value">{{ enterprise.legalPerson }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">注册资本</div>
                <div class="info-value">{{ enterprise.registeredCapital }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">成立日期</div>
                <div class="info-value">{{ enterprise.establishDate }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 位置选择器 -->
    <van-popup v-model="showLocationPicker" position="bottom">
      <van-picker
        :columns="locationOptions"
        @confirm="onLocationConfirm"
        @cancel="showLocationPicker = false"
      />
    </van-popup>

    <!-- 排序选择器 -->
    <van-popup v-model="showSortPicker" position="bottom">
      <van-picker
        :columns="sortOptions"
        @confirm="onSortConfirm"
        @cancel="showSortPicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: "EnterpriseSearch",
  data() {
    return {
      searchValue: "",
      selectedLocation: "当前位置",
      selectedSort: "智能排序",
      showLocationPicker: false,
      showSortPicker: false,
      showMoreFilters: false,
      totalCount: 24,

      categories: [
        {
          name: "龙头企业",
          icon: "shop-o",
          color: "#ff9500"
        },
        {
          name: "示范农场",
          icon: "certificate",
          color: "#1989fa"
        },
        {
          name: "示范合作社",
          icon: "friends-o",
          color: "#00c853"
        },
        {
          name: "展览场",
          icon: "photo-o",
          color: "#1989fa"
        },
        {
          name: "约站",
          icon: "calendar-o",
          color: "#7b68ee"
        }
      ],

      locationOptions: [
        "当前位置",
        "青岛市",
        "烟台市",
        "威海市",
        "济南市",
        "潍坊市"
      ],

      sortOptions: ["智能排序", "注册资本", "成立时间", "企业规模"],

      enterpriseList: [
        {
          id: 1,
          name: "青岛铜测农业有限公司",
          iconColor: "#ff9500",
          tags: ["农业", "农场"],
          legalPerson: "尚春君",
          registeredCapital: "100万元人民币",
          establishDate: "2020-09-03"
        },
        {
          id: 2,
          name: "青岛青菜农村合作社",
          iconColor: "#1989fa",
          tags: ["农业", "合作社"],
          legalPerson: "刘大民",
          registeredCapital: "800万元人民币",
          establishDate: "2019-09-04"
        },
        {
          id: 3,
          name: "青岛惠德农业有限公司",
          iconColor: "#7b68ee",
          tags: ["农业", "合作社"],
          legalPerson: "胡一天",
          registeredCapital: "200万元人民币",
          establishDate: "2019-09-04"
        }
      ]
    };
  },

  methods: {
    onClickLeft() {
      this.$router.go(-1);
    },

    onSearch(value) {
      console.log("搜索企业:", value);
      // 实现搜索逻辑
    },

    selectCategory(category) {
      console.log("选择分类:", category);
      // 根据分类筛选企业
    },

    onLocationConfirm(value) {
      this.selectedLocation = value;
      this.showLocationPicker = false;
      // 根据位置筛选
    },

    onSortConfirm(value) {
      this.selectedSort = value;
      this.showSortPicker = false;
      // 根据排序方式排序
    },

    viewEnterpriseDetail(enterprise) {
      this.$router.push({
        name: "EnterpriseDetail",
        params: { id: enterprise.id }
      });
    },

    getTagClass(tag) {
      const tagClassMap = {
        农业: "tag-agriculture",
        农场: "tag-farm",
        合作社: "tag-cooperative"
      };
      return tagClassMap[tag] || "tag-default";
    }
  }
};
</script>

<style lang="scss" scoped>
.enterprise-search {
  min-height: 100vh;
  background-color: #f7f8fa;

  .custom-nav-bar {
    background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);

    :deep(.van-nav-bar__title) {
      color: white;
      font-weight: 500;
    }

    :deep(.van-icon) {
      color: white;
    }
  }

  .search-section {
    background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
    padding: 0 16px 20px;

    .search-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .search-content {
        flex: 1;
        padding-left: 12px;

        .search-title {
          color: white;
          font-size: 20px;
          font-weight: 600;
          margin: 0 0 16px 0;
        }

        .search-input {
          padding: 0;
          :deep(.van-search) {
            padding: 0 !important;
            background: transparent;

            .van-search__content {
              background-color: white;
              border-radius: 20px;
            }

            .van-field__control {
              font-size: 14px;
            }
          }
        }
      }

      .search-illustration {
        margin-left: 16px;
        padding-top: 20px;

        img {
          width: 130px;
          height: 130px;
        }
      }
    }
  }

  .category-section {
    background-color: white;
    margin: -30px 16px 16px;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .category-title {
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      margin: 0 0 16px 0;
    }

    .category-grid {
      display: flex;
      justify-content: space-between;

      .category-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        transition: all 0.2s;

        &:active {
          transform: scale(0.95);
        }

        .category-icon {
          width: 38px;
          height: 38px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;

          .van-icon {
            color: white;
            font-size: 20px;
          }
        }

        .category-name {
          font-size: 12px;
          color: #646566;
          text-align: center;
        }
      }
    }
  }

  .filter-section {
    display: flex;
    padding: 0 16px 16px;
    gap: 12px;

    .filter-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background-color: white;
      border-radius: 16px;
      font-size: 13px;
      color: #646566;
      cursor: pointer;
      transition: all 0.2s;

      &:active {
        background-color: #f8f9fa;
      }

      .van-icon {
        margin-right: 4px;
        font-size: 14px;
      }
    }
  }

  .results-section {
    padding: 0 16px;

    .results-header {
      margin-bottom: 12px;
      font-size: 14px;
      color: #646566;
    }

    .enterprise-list {
      .enterprise-card {
        background-color: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.2s;

        &:active {
          transform: scale(0.98);
          background-color: #f8f9fa;
        }

        .enterprise-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .enterprise-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 600;
            margin-right: 12px;
          }

          .enterprise-name {
            font-size: 16px;
            font-weight: 600;
            color: #323233;
            margin: 0;
            flex: 1;
          }
        }

        .enterprise-tags {
          margin-bottom: 12px;

          .enterprise-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 8px;

            &.tag-agriculture {
              background-color: #e8f5e8;
              color: #00c853;
            }

            &.tag-farm {
              background-color: #e8f5e8;
              color: #00c853;
            }

            &.tag-cooperative {
              background-color: #e8f5e8;
              color: #00c853;
            }

            &.tag-default {
              background-color: #f7f8fa;
              color: #646566;
            }
          }
        }

        .enterprise-info {
          .info-row {
            display: flex;
            justify-content: space-between;

            .info-item {
              flex: 1;
              text-align: center;

              .info-label {
                font-size: 12px;
                color: #969799;
                margin-bottom: 4px;
              }

              .info-value {
                font-size: 13px;
                color: #323233;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
}
</style>

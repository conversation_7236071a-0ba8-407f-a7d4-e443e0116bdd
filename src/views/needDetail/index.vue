<template>
  <div class="requirement-detail">
    <!-- 头部导航 -->
    <van-nav-bar
      title="需求详情"
      left-arrow
      @click-left="onClickLeft"
      class="custom-nav-bar"
    />

    <!-- 需求基本信息 -->
    <div class="requirement-header">
      <div class="title-section">
        <h1 class="requirement-title">{{ requirementInfo.title }}</h1>
        <span class="requirement-tag">{{ requirementInfo.tag }}</span>
        <div class="tag-and-validity">
          <span class="validity-period"
            >有效至{{ requirementInfo.validUntil }}</span
          >
        </div>
      </div>
      <div class="company-name">{{ requirementInfo.companyName }}</div>
    </div>

    <!-- 操作按钮组 -->
    <div class="action-buttons">
      <span
        v-for="(button, index) in actionButtons"
        :key="index"
        class="action-btn"
        @click="handleActionClick(button)"
      >
        {{ button.text }}
      </span>
    </div>

    <!-- 详细信息卡片 -->
    <div class="detail-sections">
      <!-- 供给介绍 -->
      <div class="detail-card">
        <h3 class="section-title">供给介绍</h3>
        <p class="section-content">{{ requirementInfo.introduction }}</p>
      </div>

      <!-- 供给对象 -->
      <div class="detail-card">
        <h3 class="section-title">供给对象</h3>
        <p class="section-content">{{ requirementInfo.targetAudience }}</p>
      </div>

      <!-- 比较优势 -->
      <div class="detail-card">
        <h3 class="section-title">比较优势</h3>
        <p class="section-content">{{ requirementInfo.advantages }}</p>
      </div>

      <!-- 联系信息 -->
      <div class="detail-card contact-card">
        <h3 class="section-title">联系信息</h3>
        <div class="contact-info">
          <span class="contact-name">{{ requirementInfo.contactPerson }}</span>
          <span class="contact-phone" @click="makeCall">{{
            requirementInfo.phoneNumber
          }}</span>
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="footer-info">
      <span class="creator-info">创建人：{{ requirementInfo.creator }}</span>
      <span class="create-time"
        >创建时间：{{ requirementInfo.createTime }}</span
      >
    </div>

    <!-- 底部操作按钮 -->
    <div class="bottom-actions">
      <van-button round class="forward-btn" @click="forwardRequirement">
        转发需求
      </van-button>
      <van-button
        type="primary"
        round
        class="favorite-btn"
        @click="favoriteRequirement"
      >
        收藏需求
      </van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "RequirementDetail",
  data() {
    return {
      requirementInfo: {
        title: "短视频代运营",
        tag: "支票收",
        validUntil: "2024-11-29",
        companyName: "烟台云商来数据科技有限公司",
        introduction: "短视频代运营、短视频招聘营销运营管理",
        targetAudience: "制造业",
        advantages: "助力企业短视频软件，节省企业人力物力",
        contactPerson: "田培新",
        phoneNumber: "15615551807",
        creator: "田培新",
        createTime: "2024-11-29"
      },
      actionButtons: [
        { text: "供给类型", value: "supply-type" },
        { text: "供给类别", value: "supply-category" },
        { text: "重点产业链", value: "key-industry" }
      ]
    };
  },

  mounted() {
    // 如果有路由参数，可以根据ID获取具体需求详情
    this.loadRequirementDetail();
  },

  methods: {
    loadRequirementDetail() {
      // 模拟从API获取需求详情
      const requirementId = this.$route.params.id;
      console.log("加载需求详情:", requirementId);

      // 这里可以调用实际的API
      // this.getRequirementDetail(requirementId)
    },

    onClickLeft() {
      this.$router.go(-1);
    },

    handleActionClick(button) {
      console.log("点击操作按钮:", button);
      // 根据不同按钮执行不同操作
      switch (button.value) {
        case "supply-type":
          this.showSupplyType();
          break;
        case "supply-category":
          this.showSupplyCategory();
          break;
        case "key-industry":
          this.showKeyIndustry();
          break;
      }
    },

    showSupplyType() {
      this.$toast("查看供给类型");
    },

    showSupplyCategory() {
      this.$toast("查看供给类别");
    },

    showKeyIndustry() {
      this.$toast("查看重点产业链");
    },

    makeCall() {
      // 拨打电话
      const phoneNumber = this.requirementInfo.phoneNumber;
      this.$dialog
        .confirm({
          title: "拨打电话",
          message: `是否拨打电话：${phoneNumber}？`,
          confirmButtonText: "拨打",
          cancelButtonText: "取消"
        })
        .then(() => {
          window.location.href = `tel:${phoneNumber}`;
        })
        .catch(() => {
          // 用户取消
        });
    },

    forwardRequirement() {
      this.$toast("转发需求功能");
      // 实现转发功能
    },

    favoriteRequirement() {
      this.$toast("收藏需求成功");
      // 实现收藏功能
    }
  }
};
</script>

<style lang="scss" scoped>
.requirement-detail {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;

  .custom-nav-bar {
    background: #d4eeff;

    :deep(.van-nav-bar__title) {
      color: #323233;
      font-weight: 500;
    }
  }

  .requirement-header {
    background: linear-gradient(to bottom, #d4eeff, #f7f8fa);
    padding: 20px 16px;
    // margin-bottom: 12px;

    .title-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      //   margin-bottom: 12px;

      .requirement-title {
        font-size: 20px;
        font-weight: 600;
        color: #323233;

        // margin: 0 0 8px 0;
      }
      .requirement-tag {
        background-color: #1989fa;
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
      }

      .tag-and-validity {
        display: flex;
        align-items: center;
        gap: 12px;

        .validity-period {
          font-size: 13px;
          color: #646566;
        }
      }
    }

    .company-name {
      font-size: 14px;
      color: #646566;
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    padding: 0 16px;
    margin-bottom: 12px;

    .action-btn {
      flex: 1;
      padding: 12px;
      background: linear-gradient(#1989fa 0%, #71a8f9 100%);
      border: none;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      border-radius: 12px;
    }
  }

  .detail-sections {
    padding: 0 16px;

    .detail-card {
      background-color: white;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 12px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #323233;
        margin: 0 0 12px 0;
      }

      .section-content {
        font-size: 14px;
        color: #646566;
        line-height: 1.5;
        margin: 0;
      }

      &.contact-card {
        .contact-info {
          display: flex;
          align-items: center;
          gap: 16px;

          .contact-name {
            font-size: 14px;
            color: #323233;
          }

          .contact-phone {
            font-size: 14px;
            color: #1989fa;
            cursor: pointer;

            &:active {
              opacity: 0.7;
            }
          }
        }
      }
    }
  }

  .footer-info {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    font-size: 12px;
    color: #969799;
    background-color: transparent;
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    gap: 12px;
    padding: 12px 16px;
    background-color: white;
    border-top: 1px solid #ebedf0;
    z-index: 100;

    .forward-btn {
      flex: 1;
      height: 44px;
      font-size: 15px;
      color: #1989fa;
      border: 1px solid #1989fa;
      background-color: white;

      &:active {
        background-color: #f8f9fa;
      }
    }

    .favorite-btn {
      flex: 1;
      height: 44px;
      font-size: 15px;
      background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
      border: none;
      box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
    }
  }
}
</style>

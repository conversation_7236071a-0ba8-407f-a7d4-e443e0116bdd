<template>
  <div class="user-center">
    <!-- 头部用户信息区域 -->
    <div class="header-section">
      <div class="user-profile">
        <div class="avatar">
          {{ userInfo.trueName.charAt(1, 0) }}
        </div>
        <div class="username">{{ encipherNameComp }}</div>
      </div>

      <!-- 已获批复金额卡片 -->
    </div>
    <div class="amount-card">
      <div class="amount-value">{{ approvedAmount }}</div>
      <div class="amount-label">已获批复金额（万元）</div>
    </div>
    <!-- 功能菜单列表 -->
    <div class="menu-section">
      <div
        v-for="(item, index) in menuItems"
        :key="index"
        class="menu-item"
        @click="handleMenuClick(item)"
      >
        <div class="menu-icon">
          <van-icon :name="item.icon" />
        </div>
        <div class="menu-title">{{ item.title }}</div>
        <van-icon name="arrow" class="arrow-icon" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from "vue";
import { useRouter } from "@/composition-helpers";
import { useUserStore } from "@/store/modules/user";
import { userNameComp, encipherName } from "@/utils/hyposensitization";
const router = useRouter();
const userStore = useUserStore();
const { userInfo } = userStore;

const approvedAmount = ref("0");
const encipherNameComp = computed(() =>
  userInfo.trueName ? encipherName(userInfo.trueName) : ""
);
const menuItems = ref([
  {
    icon: "balance-o",
    title: "我的融资",
    route: "/my-financing",
    action: "financing"
  },
  {
    icon: "search",
    title: "认证企业",
    route: "/enterprise-certification",
    action: "certification"
  },
  {
    icon: "like-o",
    title: "我的收藏",
    route: "/my-favorites",
    action: "favorites"
  },
  {
    icon: "phone-o",
    title: "客服电话",
    route: "",
    action: "customer-service"
  }
]);

// 方法
const handleMenuClick = item => {
  switch (item.action) {
    case "financing":
      goToMyFinancing();
      break;
    case "certification":
      goToCertification();
      break;
    case "favorites":
      goToFavorites();
      break;
    case "customer-service":
      callCustomerService();
      break;
    default:
      if (item.route) {
        router.push(item.route);
      }
  }
};

const goToMyFinancing = () => {
  router.push("/my-financing");
};

const goToCertification = () => {
  router.push("/enterprise-certification");
};

const goToFavorites = () => {
  router.push("/my-favorites");
};

const callCustomerService = () => {
  // 拨打客服电话
  const phoneNumber = "************";
  this.$dialog
    .confirm({
      title: "客服电话",
      message: `是否拨打客服电话：${phoneNumber}？`,
      confirmButtonText: "拨打",
      cancelButtonText: "取消"
    })
    .then(() => {
      window.location.href = `tel:${phoneNumber}`;
    })
    .catch(() => {
      // 用户取消
    });
};
</script>

<style lang="scss" scoped>
.user-center {
  min-height: 100vh;
  background-color: #f7f8fa;

  .header-section {
    background: linear-gradient(135deg, #016bea 0%, #007eed 100%);
    padding: 30px 16px 30px;
    position: relative;

    .user-profile {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding: 0 20px;

      .avatar {
        width: 50px;
        height: 50px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-weight: 600;
        color: white;
        margin-right: 16px;
        border: 2px solid rgba(255, 255, 255, 0.3);
      }

      .username {
        font-size: 20px;
        font-weight: 600;
        color: white;
      }
    }
  }
  .amount-card {
    margin: -30px 24px 20px;
    background-color: white;
    border-radius: 16px;
    padding: 12px 24px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 999;
    position: relative;
    .amount-value {
      font-size: 36px;
      font-weight: 700;
      color: #ee0a24;
      margin-bottom: 8px;
    }

    .amount-label {
      font-size: 14px;
      color: #646566;
    }
  }
  .menu-section {
    // margin-top: -20px;
    padding: 0 16px;

    .menu-item {
      background-color: white;
      border-radius: 12px;
      padding: 20px 16px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

      &:active {
        transform: scale(0.98);
        background-color: #f8f9fa;
      }

      .menu-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        .van-icon {
          font-size: 20px;
          color: #323233;
        }
      }

      .menu-title {
        flex: 1;
        font-size: 16px;
        font-weight: 500;
        color: #323233;
      }

      .arrow-icon {
        color: #c8c9cc;
        font-size: 16px;
      }
    }
  }
}
</style>

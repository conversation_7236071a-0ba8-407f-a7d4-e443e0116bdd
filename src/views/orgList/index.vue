<template>
  <div class="institution-list">
    <!-- 固定头部区域 -->
    <div class="fixed-header">
      <!-- 头部导航 -->
      <van-nav-bar
        title="机构列表"
        left-arrow
        @click-left="onClickLeft"
        class="custom-nav-bar"
      >
      </van-nav-bar>
      <!-- 搜索栏 -->
      <div class="search-section">
        <van-search
          v-model="searchValue"
          placeholder="请输入关键搜索词"
          shape="round"
          background="transparent"
          @search="onSearch"
        />
      </div>
      <!-- 分类标签 -->
      <div class="category-tabs">
        <van-tabs v-model="activeCategory" @click="onCategoryChange">
          <van-tab title="银行" name="bank" />
          <van-tab title="担保" name="guarantee" />
          <van-tab title="保险" name="insurance" />
        </van-tabs>
      </div>
    </div>
    <!-- 滚动内容区域 -->
    <div class="scroll-area">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoadMore"
      >
        <div class="product-list">
          <InstitutionCard
            v-for="(product, index) in filteredInstitutions"
            :key="product.id || index"
            :institution="product"
          />
          <van-empty
            v-if="!loading && filteredInstitutions.length === 0"
            description="暂无符合条件的产品"
            image="search"
          />
        </div>
      </van-list>
      <van-empty
        v-if="filteredInstitutions.length === 0"
        description="暂无相关机构"
        image="search"
      />
    </div>
  </div>
</template>

<script>
import InstitutionCard from "@/components/InstitutionCard";
import { getOrgList } from "@/api/orgList";

export default {
  name: "InstitutionList",
  components: {
    InstitutionCard
  },
  data() {
    return {
      finished: false,
      loading: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        data: {}
        // searchValue: ""
      },
      searchValue: "",
      activeCategory: "bank",
      institutionList: []
    };
  },
  computed: {
    filteredInstitutions() {
      return this.institutionList;
    }
  },
  created() {
    this.resetAndLoad();
  },
  methods: {
    async resetAndLoad() {
      this.page = 1;
      this.finished = false;
      this.institutionList = [];
      await this.getInstitutionList();
    },
    async getInstitutionList() {
      if (this.finished) return;
      this.loading = true;
      const { records, total } = await getOrgList(this.queryParams);
      if (this.page === 1) {
        this.institutionList = records;
      } else {
        this.institutionList = this.institutionList.concat(records);
      }
      this.total = total;
      this.loading = false;
      if (this.institutionList.length >= total) {
        this.finished = true;
      }
    },
    onLoadMore() {
      console.log("加载更多");
      if (this.finished) return;
      this.page++;
      this.getInstitutionList();
    },
    onClickLeft() {
      this.$router.go(-1);
    },
    onScan() {
      console.log("扫码功能");
    },
    onSearch(value) {
      console.log("搜索:", value);
      // this.queryParams.data.jpmc = value;
      this.$set(this.queryParams.data, "jpmc", value);
      console.log(this.queryParams);
      this.resetAndLoad();
    },
    onCategoryChange(name, title) {
      this.activeCategory = name;
      this.resetAndLoad();
    },
    onInstitutionClick(institution) {
      this.$router.push({
        name: "InstitutionDetail",
        params: { id: institution.id }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.institution-list {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
  overflow: hidden;

  .fixed-header {
    position: sticky;
    top: 0;
    z-index: 10;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
  }

  .scroll-area {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    background: #f7f8fa;
  }

  .custom-nav-bar {
    background-color: white;

    :deep(.van-nav-bar__title) {
      color: #323233;
      font-weight: 500;
    }

    .scan-icon {
      color: #969799;
      font-size: 18px;
    }
  }

  .search-section {
    padding: 12px 16px;
    background-color: white;

    :deep(.van-search) {
      padding: 0;

      .van-search__content {
        background-color: #f7f8fa;
        border-radius: 20px;
      }

      .van-field__control {
        font-size: 14px;
      }
    }
  }

  .category-tabs {
    background-color: white;
    border-bottom: 1px solid #ebedf0;

    :deep(.van-tabs) {
      .van-tabs__wrap {
        padding: 0 16px;
      }

      .van-tab {
        font-size: 15px;
        color: #646566;

        &.van-tab--active {
          color: #1989fa;
          font-weight: 500;
        }
      }

      .van-tabs__line {
        background-color: #1989fa;
        width: 20px;
        border-radius: 2px;
      }
    }
  }

  .institution-content {
    padding: 8px 16px 16px;

    :deep(.van-empty) {
      padding: 60px 0;
    }
  }
}
</style>

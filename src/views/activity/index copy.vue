<template>
  <div class="activity-calendar">
    <!-- 头部标题 -->
    <div class="header">
      <div class="title">七月活动日历</div>
      <!-- <div class="illustration">
        <div class="calendar-icon">
          <div class="calendar-header"></div>
          <div class="calendar-body">
            <div class="calendar-grid">
              <div class="grid-item"></div>
              <div class="grid-item"></div>
              <div class="grid-item"></div>
              <div class="grid-item active"></div>
              <div class="grid-item"></div>
              <div class="grid-item"></div>
            </div>
          </div>
          <div class="floating-elements">
            <div class="element element-1">🎉</div>
            <div class="element element-2">📅</div>
            <div class="element element-3">⭐</div>
          </div>
        </div>
      </div> -->
    </div>

    <!-- 自定义日历 -->
    <div class="calendar-container">
      <!-- <div class="calendar-header">
        <div class="month-nav">
          <van-icon name="arrow-left" @click="prevMonth" />
          <span class="month-title">{{ currentMonthText }}</span>
          <van-icon name="arrow" @click="nextMonth" />
        </div>
      </div> -->

      <div class="calendar-weekdays">
        <div class="weekday" v-for="day in weekdays" :key="day">{{ day }}</div>
      </div>

      <div class="calendar-days">
        <div
          v-for="(day, index) in calendarDays"
          :key="index"
          class="calendar-day"
          :class="{
            'other-month': !day.isCurrentMonth,
            selected: isSelectedDate(day.date),
            'has-event': day.events.length > 0,
            today: isToday(day.date)
          }"
          @click="selectDate(day.date)"
        >
          <div class="day-number">{{ day.day }}</div>
          <div class="day-events" v-if="day.events.length > 0">
            <div
              v-for="event in day.events.slice(0, 2)"
              :key="event.id"
              class="event-dot"
              :class="event.type"
            ></div>
            <div v-if="day.events.length > 2" class="more-events">
              +{{ day.events.length - 2 }}
            </div>
          </div>
          <div class="day-info" v-if="day.events.length > 0">
            {{ day.events[0].name }}
          </div>
        </div>
      </div>
    </div>

    <!-- 时间轴 -->
    <div class="timeline-container" ref="timelineContainer">
      <div class="timeline-header">
        <van-icon name="clock-o" />
        <span class="timeline-title">{{ selectedDateText }}活动详情</span>
      </div>

      <div class="timeline-content" ref="timelineContent">
        <div v-if="selectedDateEvents.length === 0" class="no-events">
          <van-icon name="calendar-o" />
          <p>当天暂无活动安排</p>
        </div>

        <div
          v-for="(event, index) in selectedDateEvents"
          :key="event.id"
          :ref="`timeline-item-${event.date}`"
          class="timeline-item"
          :class="event.type"
        >
          <div class="timeline-marker">
            <div class="marker-dot" :class="event.type"></div>
            <div
              class="marker-line"
              v-if="index < selectedDateEvents.length - 1"
            ></div>
          </div>

          <div class="timeline-content-item">
            <div class="event-time" v-if="event.time">{{ event.time }}</div>
            <div class="event-title">{{ event.title }}</div>
            <div class="event-description">{{ event.description }}</div>
            <div class="event-tags">
              <span class="event-tag" :class="event.type">
                {{ getEventTypeText(event.type) }}
              </span>
              <span v-if="event.location" class="event-location">
                <van-icon name="location-o" />
                {{ event.location }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Icon } from "vant";

export default {
  name: "ActivityCalendar",
  components: {
    [Icon.name]: Icon
  },
  data() {
    return {
      selectedDate: new Date(),
      currentDate: new Date(),
      weekdays: ["日", "一", "二", "三", "四", "五", "六"],
      // 活动数据
      events: [
        {
          id: 1,
          date: "2024-07-01",
          title: "建党节",
          name: "建党节",
          description: "今天是中国共产党成立纪念日，全国各地举行庆祝活动。",
          type: "festival",
          time: "全天",
          location: "全国各地"
        },
        {
          id: 2,
          date: "2024-07-02",
          title: "夏季活动",
          name: "夏季活动",
          description: "上午9点，上午茶，小憩只是放松身心的开始。",
          type: "activity",
          time: "09:00",
          location: "会议室A"
        },
        {
          id: 3,
          date: "2024-07-06",
          title: "小暑",
          name: "小暑",
          description: "二十四节气中的第十一个节气，夏季的第五个节气。",
          type: "solar-term",
          time: "全天"
        },
        {
          id: 4,
          date: "2024-07-10",
          title: "三伏·初伏",
          name: "三伏",
          description: "一年中气温最高且又潮湿、闷热的日子。",
          type: "solar-term",
          time: "全天"
        },
        {
          id: 5,
          date: "2024-07-15",
          title: "企业培训活动",
          name: "培训",
          description: "针对企业管理人员的专业培训课程，提升管理技能。",
          type: "training",
          time: "14:00",
          location: "培训中心"
        },
        {
          id: 6,
          date: "2024-07-15",
          title: "团队建设活动",
          name: "团建",
          description: "增强团队凝聚力的户外拓展活动。",
          type: "activity",
          time: "16:00",
          location: "户外基地"
        },
        {
          id: 7,
          date: "2024-07-20",
          title: "夏季促销活动",
          name: "促销",
          description: "各大商场开展夏季促销活动，优惠力度空前。",
          type: "promotion",
          time: "10:00",
          location: "商业中心"
        },
        {
          id: 8,
          date: "2024-07-23",
          title: "大暑",
          name: "大暑",
          description: "二十四节气中的第十二个节气，夏季最后一个节气。",
          type: "solar-term",
          time: "全天"
        },
        {
          id: 9,
          date: "2024-07-26",
          title: "社区文化节",
          name: "文化节",
          description: "社区举办文化节活动，展示本地特色文化。",
          type: "culture",
          time: "19:00",
          location: "社区广场"
        }
      ]
    };
  },
  computed: {
    // 当前月份文本
    currentMonthText() {
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth() + 1;
      return `${year}年${month}月`;
    },

    // 选中日期文本
    selectedDateText() {
      if (!this.selectedDate) return "";
      const month = this.selectedDate.getMonth() + 1;
      const day = this.selectedDate.getDate();
      return `${month}月${day}日`;
    },

    // 日历天数数组
    calendarDays() {
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth();

      // 获取当月第一天和最后一天
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);

      // 获取第一天是星期几
      const firstDayWeek = firstDay.getDay();

      // 获取上个月的最后几天
      const prevMonthLastDay = new Date(year, month, 0).getDate();

      const days = [];

      // 添加上个月的日期
      for (let i = firstDayWeek - 1; i >= 0; i--) {
        const day = prevMonthLastDay - i;
        const date = new Date(year, month - 1, day);
        days.push({
          day,
          date,
          isCurrentMonth: false,
          events: this.getEventsForDate(date)
        });
      }

      // 添加当月的日期
      for (let day = 1; day <= lastDay.getDate(); day++) {
        const date = new Date(year, month, day);
        days.push({
          day,
          date,
          isCurrentMonth: true,
          events: this.getEventsForDate(date)
        });
      }

      // 添加下个月的日期，补齐42个格子
      const remainingDays = 42 - days.length;
      for (let day = 1; day <= remainingDays; day++) {
        const date = new Date(year, month + 1, day);
        days.push({
          day,
          date,
          isCurrentMonth: false,
          events: this.getEventsForDate(date)
        });
      }

      return days;
    },

    // 选中日期的事件
    selectedDateEvents() {
      if (!this.selectedDate) return [];
      return this.getEventsForDate(this.selectedDate);
    }
  },
  methods: {
    // 获取指定日期的事件
    getEventsForDate(date) {
      const dateStr = this.formatDate(date);
      return this.events.filter(event => event.date === dateStr);
    },

    // 格式化日期为字符串
    formatDate(date) {
      if (!date) return "";
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    // 判断是否为选中日期
    isSelectedDate(date) {
      if (!this.selectedDate || !date) return false;
      return this.formatDate(date) === this.formatDate(this.selectedDate);
    },

    // 判断是否为今天
    isToday(date) {
      const today = new Date();
      return this.formatDate(date) === this.formatDate(today);
    },

    // 选择日期
    selectDate(date) {
      this.selectedDate = date;
      this.scrollToTimeline();
    },

    // 上一个月
    prevMonth() {
      this.currentDate = new Date(
        this.currentDate.getFullYear(),
        this.currentDate.getMonth() - 1,
        1
      );
    },

    // 下一个月
    nextMonth() {
      this.currentDate = new Date(
        this.currentDate.getFullYear(),
        this.currentDate.getMonth() + 1,
        1
      );
    },

    // 滚动到时间轴
    scrollToTimeline() {
      this.$nextTick(() => {
        const container = this.$refs.timelineContainer;
        if (container) {
          container.scrollTo({
            top: 0,
            behavior: "smooth"
          });
        }
      });
    },

    // 获取事件类型文本
    getEventTypeText(type) {
      const typeMap = {
        festival: "节日",
        "solar-term": "节气",
        activity: "活动",
        training: "培训",
        promotion: "促销",
        culture: "文化",
        business: "商务"
      };
      return typeMap[type] || "其他";
    }
  },

  mounted() {
    // 初始化时设置当前月份为7月
    this.currentDate = new Date(2024, 6, 1); // 7月是索引6
    this.selectedDate = new Date(2024, 6, 1);
  }
};
</script>

<style lang="scss" scoped>
.activity-calendar {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #0070ec 100%);
  padding: 20px 16px;

  .header {
    position: relative;
    text-align: center;
    margin-bottom: 20px;

    .title {
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      margin-bottom: 10px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .illustration {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 20px;
      position: relative;

      .calendar-icon {
        position: relative;
        width: 120px;
        height: 100px;

        .calendar-header {
          width: 80px;
          height: 20px;
          background: #fff;
          border-radius: 8px 8px 0 0;
          margin: 0 auto;
          position: relative;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

          &::before,
          &::after {
            content: "";
            position: absolute;
            top: -8px;
            width: 4px;
            height: 16px;
            background: #ff6b6b;
            border-radius: 2px;
          }

          &::before {
            left: 15px;
          }

          &::after {
            right: 15px;
          }
        }

        .calendar-body {
          width: 80px;
          height: 60px;
          background: #fff;
          border-radius: 0 0 8px 8px;
          margin: 0 auto;
          padding: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

          .calendar-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 4px;
            height: 100%;

            .grid-item {
              background: #f0f0f0;
              border-radius: 2px;
              transition: all 0.3s ease;

              &.active {
                background: linear-gradient(135deg, #0070ec, #0070ec);
                transform: scale(1.1);
                box-shadow: 0 2px 4px rgba(102, 126, 234, 0.4);
              }
            }
          }
        }

        .floating-elements {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;

          .element {
            position: absolute;
            font-size: 20px;
            animation: float 3s ease-in-out infinite;

            &.element-1 {
              top: 10px;
              left: 10px;
              animation-delay: 0s;
            }

            &.element-2 {
              top: 20px;
              right: 10px;
              animation-delay: 1s;
            }

            &.element-3 {
              bottom: 10px;
              left: 20px;
              animation-delay: 2s;
            }
          }
        }
      }
    }

    @keyframes float {
      0%,
      100% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
    }
  }

  .calendar-container {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    margin-bottom: 20px;

    .calendar-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 16px 20px;

      .month-nav {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .van-icon {
          color: #fff;
          font-size: 20px;
          cursor: pointer;
          padding: 8px;
          border-radius: 50%;
          transition: background-color 0.3s ease;

          &:hover {
            background-color: rgba(255, 255, 255, 0.2);
          }
        }

        .month-title {
          color: #fff;
          font-size: 18px;
          font-weight: bold;
        }
      }
    }

    .calendar-weekdays {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      background: #f8f9fa;

      .weekday {
        padding: 12px 0;
        text-align: center;
        font-size: 14px;
        font-weight: 500;
        color: #666;
      }
    }

    .calendar-days {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      background: #fff;

      .calendar-day {
        position: relative;
        min-height: 30px;
        padding: 2px 4px;
        border: 1px solid #f0f0f0;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;

        &:hover {
          background-color: #f8f9fa;
        }

        &.other-month {
          color: #ccc;
          background-color: #fafafa;

          .day-number {
            color: #ccc;
          }
        }

        &.today {
          .day-number {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: #fff;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        &.selected {
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: #fff;

          .day-number {
            color: #fff;
          }

          .day-info {
            color: rgba(255, 255, 255, 0.9);
          }
        }

        &.has-event {
          .day-number {
            font-weight: bold;
          }
        }

        .day-number {
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .day-events {
          display: flex;
          gap: 2px;
          margin-bottom: 2px;

          .event-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;

            &.festival {
              background: #ff6b6b;
            }

            &.solar-term {
              background: #feca57;
            }

            &.activity {
              background: #4ecdc4;
            }

            &.training {
              background: #48cae4;
            }

            &.promotion {
              background: #f72585;
            }

            &.culture {
              background: #7209b7;
            }
          }

          .more-events {
            font-size: 10px;
            color: #666;
          }
        }

        .day-info {
          font-size: 10px;
          color: #666;
          text-align: center;
          line-height: 1.2;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
        }
      }
    }
  }

  .timeline-container {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    max-height: 400px;
    overflow-y: auto;

    .timeline-header {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 16px 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
      border-radius: 16px 16px 0 0;

      .van-icon {
        font-size: 18px;
      }

      .timeline-title {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .timeline-content {
      padding: 20px;

      .no-events {
        text-align: center;
        padding: 40px 20px;
        color: #999;

        .van-icon {
          font-size: 48px;
          margin-bottom: 16px;
          color: #ddd;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }

      .timeline-item {
        display: flex;
        margin-bottom: 24px;
        position: relative;

        &:last-child {
          margin-bottom: 0;

          .timeline-marker .marker-line {
            display: none;
          }
        }

        .timeline-marker {
          position: relative;
          margin-right: 16px;
          display: flex;
          flex-direction: column;
          align-items: center;

          .marker-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 3px solid #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

            &.festival {
              background: #ff6b6b;
            }

            &.solar-term {
              background: #feca57;
            }

            &.activity {
              background: #4ecdc4;
            }

            &.training {
              background: #48cae4;
            }

            &.promotion {
              background: #f72585;
            }

            &.culture {
              background: #7209b7;
            }
          }

          .marker-line {
            width: 2px;
            height: 40px;
            background: #e0e0e0;
            margin-top: 8px;
          }
        }

        .timeline-content-item {
          flex: 1;
          padding-top: 2px;

          .event-time {
            font-size: 12px;
            color: #999;
            margin-bottom: 4px;
          }

          .event-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
          }

          .event-description {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 12px;
          }

          .event-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center;

            .event-tag {
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: 500;
              border: 1px solid;

              &.festival {
                background: rgba(255, 107, 107, 0.1);
                color: #ff6b6b;
                border-color: #ff6b6b;
              }

              &.solar-term {
                background: rgba(254, 202, 87, 0.1);
                color: #feca57;
                border-color: #feca57;
              }

              &.activity {
                background: rgba(78, 205, 196, 0.1);
                color: #4ecdc4;
                border-color: #4ecdc4;
              }

              &.training {
                background: rgba(72, 202, 228, 0.1);
                color: #48cae4;
                border-color: #48cae4;
              }

              &.promotion {
                background: rgba(247, 37, 133, 0.1);
                color: #f72585;
                border-color: #f72585;
              }

              &.culture {
                background: rgba(114, 9, 183, 0.1);
                color: #7209b7;
                border-color: #7209b7;
              }
            }

            .event-location {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: #999;

              .van-icon {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

// 滚动条样式
.timeline-container::-webkit-scrollbar {
  width: 6px;
}

.timeline-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.timeline-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

.timeline-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}
</style>

<template>
  <div class="activity-calendar">
    <!-- 头部装饰区域 -->
    <div class="calendar-header">
      <div class="decoration-dots">
        <div class="dot dot-1"></div>
        <div class="dot dot-2"></div>
        <div class="dot dot-3"></div>
        <div class="dot dot-4"></div>
      </div>

      <div class="header-content">
        <h1 class="header-title">{{ currentMonthName }}活动日历</h1>
        <div class="header-illustration">
          <div class="calendar-icon">
            <van-icon name="calendar-o" />
          </div>
          <div class="emoji">📅</div>
          <div class="circle-decoration"></div>
        </div>
      </div>
    </div>

    <!-- 日历主体 -->
    <div class="calendar-body">
      <!-- 月份导航 -->
      <div class="month-navigation">
        <van-button
          icon="arrow-left"
          type="primary"
          plain
          size="small"
          @click="changeMonth('prev')"
        />
        <h2 class="month-title">{{ currentMonthName }}活动日历</h2>
        <van-button
          icon="arrow"
          type="primary"
          plain
          size="small"
          @click="changeMonth('next')"
        />
      </div>

      <!-- 星期标题 -->
      <div class="weekdays">
        <div v-for="day in weekdays" :key="day" class="weekday">
          {{ day }}
        </div>
      </div>

      <!-- 日历网格 -->
      <div class="calendar-grid">
        <div
          v-for="(day, index) in calendarDays"
          :key="index"
          class="calendar-day"
          :class="{
            'is-empty': day === null,
            'is-selected': selectedDate === day,
            'is-today': isToday(day),
            'has-event': day && getEventForDate(day)
          }"
          @click="handleDateClick(day)"
        >
          <template v-if="day">
            <div class="day-number">{{ day }}</div>
            <div class="lunar-info">{{ getLunarInfo(day) }}</div>
            <div
              v-if="getEventForDate(day)"
              class="event-tag"
              :class="getEventForDate(day).color"
            >
              {{ getEventForDate(day).name }}
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 时间轴区域 -->
    <div class="timeline-section">
      <div class="timeline-header">
        <van-icon name="clock-o" />
        <h3>活动时间轴</h3>
      </div>

      <div class="timeline-container" ref="timelineContainer">
        <div
          v-for="(event, index) in timelineEvents"
          :key="index"
          class="timeline-event"
          :class="{ 'highlight-event': highlightedEventIndex === index }"
          :ref="`timelineEvent${index}`"
        >
          <div class="timeline-dot-container">
            <div
              class="timeline-dot"
              :class="getTimelineDotClass(event.type)"
            ></div>
            <div
              v-if="index < timelineEvents.length - 1"
              class="timeline-line"
            ></div>
          </div>

          <div class="timeline-content">
            <div class="timeline-date-title">
              <span class="timeline-date">{{ event.date }}</span>
              <span class="timeline-title">{{ event.title }}</span>
            </div>
            <p class="timeline-description">{{ event.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ActivityCalendar",
  data() {
    return {
      currentDate: new Date(2024, 6, 1), // July 2024
      selectedDate: null,
      highlightedEventIndex: -1,

      weekdays: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],

      // 日历事件数据
      calendarEvents: [
        {
          date: 1,
          type: "festival",
          name: "建党节",
          description: "中国共产党成立纪念日",
          color: "event-red"
        },
        {
          date: 7,
          type: "solar",
          name: "小暑",
          description: "二十四节气之一",
          color: "event-orange"
        },
        {
          date: 8,
          type: "activity",
          name: "初一",
          description: "农历初一",
          color: "event-blue"
        },
        {
          date: 16,
          type: "solar",
          name: "三伏·初伏",
          description: "一年中气温最高的时期",
          color: "event-yellow"
        },
        {
          date: 23,
          type: "solar",
          name: "大暑",
          description: "二十四节气之一",
          color: "event-red-dark"
        },
        {
          date: 26,
          type: "activity",
          name: "中秋",
          description: "传统节日",
          color: "event-yellow-dark"
        }
      ],

      // 时间轴事件数据
      timelineEvents: [
        {
          date: "7月1日",
          title: "建党节",
          description:
            "今天是中国共产党成立103周年，值得铭记，不忘初心，牢记使命，继续为国家建设贡献力量，紫荆花开月，共庆建党节。",
          type: "festival"
        },
        {
          date: "7月7日",
          title: "小暑",
          description: "小暑大暑，上蒸下煮。小暑只是炎夏天气的开始。",
          type: "solar"
        },
        {
          date: "7月16日",
          title: "三伏·初伏",
          description: "是一年中气温最高且又潮湿的时候。",
          type: "solar"
        },
        {
          date: "7月23日",
          title: "大暑",
          description:
            '是一年中日照最多、最炎热的节气。"湿热交蒸"在此时到达顶点。',
          type: "solar"
        }
      ]
    };
  },

  computed: {
    currentMonthName() {
      return this.currentDate.toLocaleDateString("zh-CN", { month: "long" });
    },

    year() {
      return this.currentDate.getFullYear();
    },

    month() {
      return this.currentDate.getMonth();
    },

    firstDayOfMonth() {
      return new Date(this.year, this.month, 1).getDay();
    },

    daysInMonth() {
      return new Date(this.year, this.month + 1, 0).getDate();
    },

    calendarDays() {
      const days = [];

      // 添加空白天数
      for (let i = 0; i < this.firstDayOfMonth; i++) {
        days.push(null);
      }

      // 添加当月天数
      for (let day = 1; day <= this.daysInMonth; day++) {
        days.push(day);
      }

      return days;
    }
  },

  methods: {
    // 获取指定日期的事件
    getEventForDate(date) {
      return this.calendarEvents.find(event => event.date === date);
    },

    // 获取农历信息（简化版）
    getLunarInfo(date) {
      const lunarDays = [
        "初一",
        "初二",
        "初三",
        "初四",
        "初五",
        "初六",
        "初七",
        "初八",
        "初九",
        "初十",
        "十一",
        "十二",
        "十三",
        "十四",
        "十五",
        "十六",
        "十七",
        "十八",
        "十九",
        "二十",
        "廿一",
        "廿二",
        "廿三",
        "廿四",
        "廿五",
        "廿六",
        "廿七",
        "廿八",
        "廿九",
        "三十"
      ];
      return lunarDays[(date - 1) % 30];
    },

    // 判断是否为今天
    isToday(date) {
      if (!date) return false;
      const today = new Date();
      return (
        date === today.getDate() &&
        this.month === today.getMonth() &&
        this.year === today.getFullYear()
      );
    },

    // 点击日期处理
    handleDateClick(date) {
      if (!date) return;

      this.selectedDate = date;

      // 查找对应的时间轴事件
      const eventIndex = this.timelineEvents.findIndex(event => {
        const eventDate = parseInt(event.date.replace(/[^\d]/g, ""));
        return eventDate === date;
      });

      if (eventIndex !== -1) {
        this.scrollToTimelineEvent(eventIndex);
      }
    },

    // 滚动到时间轴事件
    scrollToTimelineEvent(index) {
      this.$nextTick(() => {
        const container = this.$refs.timelineContainer;
        const eventRefs = this.$refs[`timelineEvent${index}`];

        if (container && eventRefs && eventRefs[0]) {
          const eventElement = eventRefs[0];

          // 计算滚动位置
          const containerTop = container.offsetTop;
          const eventTop = eventElement.offsetTop;
          const scrollTop = eventTop - containerTop - 50;

          // 平滑滚动
          container.scrollTo({
            top: scrollTop,
            behavior: "smooth"
          });

          // 高亮效果
          this.highlightedEventIndex = index;
          setTimeout(() => {
            this.highlightedEventIndex = -1;
          }, 2000);
        }
      });
    },

    // 切换月份
    changeMonth(direction) {
      const newDate = new Date(this.currentDate);
      if (direction === "prev") {
        newDate.setMonth(this.month - 1);
      } else {
        newDate.setMonth(this.month + 1);
      }
      this.currentDate = newDate;
      this.selectedDate = null;
      this.highlightedEventIndex = -1;
    },

    // 获取时间轴圆点样式
    getTimelineDotClass(type) {
      const classMap = {
        festival: "dot-red",
        solar: "dot-orange",
        activity: "dot-blue"
      };
      return classMap[type] || "dot-blue";
    }
  }
};
</script>

<style lang="scss" scoped>
.activity-calendar {
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 50%, #1e5f99 100%);
  border-radius: 24px;

  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);

  // 头部装饰区域
  .calendar-header {
    position: relative;
    padding: 32px 24px;
    color: white;
    overflow: hidden;

    .decoration-dots {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;

      .dot {
        position: absolute;
        border-radius: 50%;

        &.dot-1 {
          top: 16px;
          left: 24px;
          width: 16px;
          height: 16px;
          background-color: #ffd700;
        }

        &.dot-2 {
          top: 32px;
          right: 32px;
          width: 12px;
          height: 12px;
          background-color: #00ff7f;
        }

        &.dot-3 {
          top: 48px;
          left: 33%;
          width: 8px;
          height: 32px;
          background-color: #ff6b6b;
          border-radius: 4px;
          transform: rotate(12deg);
        }

        &.dot-4 {
          top: 24px;
          right: 25%;
          width: 24px;
          height: 8px;
          background-color: #ffe135;
          border-radius: 4px;
          transform: rotate(-12deg);
        }
      }
    }

    .header-content {
      position: relative;
      z-index: 2;
      text-align: center;

      .header-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 24px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .header-illustration {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16px;

        .calendar-icon {
          width: 80px;
          height: 60px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;

          .van-icon {
            font-size: 32px;
          }
        }

        .emoji {
          font-size: 48px;
          opacity: 0.3;
        }

        .circle-decoration {
          width: 48px;
          height: 48px;
          background-color: #ff8c42;
          border-radius: 50%;
        }
      }
    }
  }

  // 日历主体
  .calendar-body {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    width: 100%;
    .month-navigation {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 24px;
      color: white;

      .month-title {
        font-size: 20px;
        font-weight: 600;
        margin: 0;
      }

      :deep(.van-button) {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      }
    }

    .weekdays {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 4px;
      padding: 8px 16px;

      .weekday {
        text-align: center;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
        padding: 8px 0;
        font-size: 14px;
      }
    }

    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 4px;
      padding: 0 16px 24px;

      .calendar-day {
        aspect-ratio: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;

        &:not(.is-empty):hover {
          background: rgba(255, 255, 255, 0.2);
          transform: scale(1.05);
        }

        &.is-selected {
          background: rgba(255, 255, 255, 0.3);
          box-shadow: 0 0 0 2px white;
        }

        &.is-today {
          background: rgba(255, 215, 0, 0.3);
        }

        .day-number {
          color: white;
          font-weight: 600;
          font-size: 16px;
          margin-bottom: 2px;
        }

        .lunar-info {
          color: rgba(255, 255, 255, 0.7);
          font-size: 10px;
          margin-bottom: 4px;
        }

        .event-tag {
          font-size: 10px;
          padding: 2px 6px;
          border-radius: 8px;
          color: white;
          font-weight: 500;
          text-align: center;
          min-width: 0;

          &.event-red {
            background-color: #ef4444;
          }

          &.event-orange {
            background-color: #f97316;
          }

          &.event-blue {
            background-color: #3b82f6;
          }

          &.event-yellow {
            background-color: #eab308;
          }

          &.event-red-dark {
            background-color: #dc2626;
          }

          &.event-yellow-dark {
            background-color: #d97706;
          }
        }
      }
    }
  }

  // 时间轴区域
  .timeline-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
    padding: 24px;

    .timeline-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      color: white;

      .van-icon {
        margin-right: 8px;
        font-size: 20px;
      }

      h3 {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
      }
    }

    .timeline-container {
      max-height: 320px;
      overflow-y: auto;
      padding-right: 8px;

      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;

        &:hover {
          background: rgba(255, 255, 255, 0.5);
        }
      }

      .timeline-event {
        display: flex;
        margin-bottom: 24px;
        transition: all 0.3s ease;

        &.highlight-event {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          padding: 8px;
          transform: scale(1.02);
        }

        .timeline-dot-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-right: 16px;

          .timeline-dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            flex-shrink: 0;

            &.dot-red {
              background-color: #ef4444;
            }

            &.dot-orange {
              background-color: #f97316;
            }

            &.dot-blue {
              background-color: #3b82f6;
            }
          }

          .timeline-line {
            width: 2px;
            height: 64px;
            background: rgba(255, 255, 255, 0.3);
            margin-top: 8px;
          }
        }

        .timeline-content {
          flex: 1;
          padding-bottom: 16px;

          .timeline-date-title {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;

            .timeline-date {
              color: white;
              font-weight: 500;
              font-size: 14px;
            }

            .timeline-title {
              color: #ffd700;
              font-weight: 600;
              font-size: 16px;
            }
          }

          .timeline-description {
            color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
            line-height: 1.6;
            margin: 0;
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="activity-calendar">
    <!-- 头部标题 -->
    <div class="header">
      <div class="title">七月活动日历</div>
      <div class="illustration">
        <div class="calendar-icon">
          <div class="calendar-header"></div>
          <div class="calendar-body">
            <div class="calendar-grid">
              <div class="grid-item"></div>
              <div class="grid-item"></div>
              <div class="grid-item"></div>
              <div class="grid-item active"></div>
              <div class="grid-item"></div>
              <div class="grid-item"></div>
            </div>
          </div>
          <div class="floating-elements">
            <div class="element element-1">🎉</div>
            <div class="element element-2">📅</div>
            <div class="element element-3">⭐</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日历组件 -->
    <div class="calendar-container">
      <van-calendar
        v-model="selectedDate"
        :show-title="false"
        :show-subtitle="false"
        :show-confirm="false"
        :poppable="false"
        :formatter="dayFormatter"
        @select="onDateSelect"
        class="custom-calendar"
      />
    </div>

    <!-- 时间轴 -->
    <div class="timeline-container" ref="timelineContainer">
      <div class="timeline-title">活动时间轴</div>
      <div class="timeline-content" ref="timelineContent">
        <div
          v-for="(item, index) in timelineData"
          :key="index"
          :ref="`timeline-item-${item.date}`"
          class="timeline-item"
          :class="{
            active: selectedDate && formatDate(selectedDate) === item.date
          }"
        >
          <div class="timeline-date">
            <div class="date-circle">{{ item.day }}</div>
            <div class="date-text">{{ item.dateText }}</div>
          </div>
          <div class="timeline-content-item">
            <div class="event-title">{{ item.title }}</div>
            <div class="event-description">{{ item.description }}</div>
            <div class="event-tags">
              <span
                v-for="tag in item.tags"
                :key="tag"
                class="event-tag"
                :class="tag.type"
              >
                {{ tag.name }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Calendar } from "vant";

export default {
  name: "ActivityCalendar",
  components: {
    [Calendar.name]: Calendar
  },
  data() {
    return {
      selectedDate: new Date(),
      // 模拟的活动数据
      timelineData: [
        {
          date: "2024-07-01",
          day: "1",
          dateText: "7月1日",
          title: "建党节",
          description: "今天是中国共产党成立纪念日，全国各地举行庆祝活动。",
          tags: [{ name: "节日", type: "festival" }]
        },
        {
          date: "2024-07-02",
          day: "2",
          dateText: "7月2日",
          title: "夏季活动",
          description: "上午9点，上午茶，小憩只是放松身心的开始。",
          tags: [{ name: "活动", type: "activity" }]
        },
        {
          date: "2024-07-06",
          day: "6",
          dateText: "7月6日",
          title: "小暑",
          description: "二十四节气中的第十一个节气，夏季的第五个节气。",
          tags: [{ name: "节气", type: "solar-term" }]
        },
        {
          date: "2024-07-10",
          day: "10",
          dateText: "7月10日",
          title: "三伏·初伏",
          description: "一年中气温最高且又潮湿、闷热的日子。",
          tags: [{ name: "节气", type: "solar-term" }]
        },
        {
          date: "2024-07-15",
          day: "15",
          dateText: "7月15日",
          title: "企业培训活动",
          description: "针对企业管理人员的专业培训课程，提升管理技能。",
          tags: [
            { name: "培训", type: "training" },
            { name: "企业", type: "business" }
          ]
        },
        {
          date: "2024-07-20",
          day: "20",
          dateText: "7月20日",
          title: "夏季促销活动",
          description: "各大商场开展夏季促销活动，优惠力度空前。",
          tags: [{ name: "促销", type: "promotion" }]
        },
        {
          date: "2024-07-23",
          day: "23",
          dateText: "7月23日",
          title: "大暑",
          description: "二十四节气中的第十二个节气，夏季最后一个节气。",
          tags: [{ name: "节气", type: "solar-term" }]
        },
        {
          date: "2024-07-26",
          day: "26",
          dateText: "7月26日",
          title: "社区文化节",
          description: "社区举办文化节活动，展示本地特色文化。",
          tags: [
            { name: "文化", type: "culture" },
            { name: "社区", type: "community" }
          ]
        }
      ]
    };
  },
  methods: {
    // 日期格式化器，用于在日历上显示特殊标记
    dayFormatter(day) {
      const dateStr = this.formatDate(day.date);
      const eventData = this.timelineData.find(item => item.date === dateStr);

      if (eventData) {
        day.bottomInfo = eventData.tags[0]?.name || "";
        day.className = `has-event ${eventData.tags[0]?.type || ""}`;
      }

      return day;
    },

    // 日期选择处理
    onDateSelect(date) {
      this.selectedDate = date;
      this.scrollToTimelineItem(date);
    },

    // 滚动到对应的时间轴项目
    scrollToTimelineItem(date) {
      const dateStr = this.formatDate(date);
      const targetRef = `timeline-item-${dateStr}`;

      this.$nextTick(() => {
        const targetElement = this.$refs[targetRef];
        if (targetElement && targetElement[0]) {
          const container = this.$refs.timelineContainer;
          const element = targetElement[0];

          // 计算滚动位置
          const containerTop = container.offsetTop;
          const elementTop = element.offsetTop;
          const scrollTop = elementTop - containerTop - 100; // 留一些边距

          // 平滑滚动
          container.scrollTo({
            top: scrollTop,
            behavior: "smooth"
          });
        }
      });
    },

    // 格式化日期为字符串
    formatDate(date) {
      if (!date) return "";
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    }
  },

  mounted() {
    // 初始化时滚动到当前选中日期
    if (this.selectedDate) {
      this.scrollToTimelineItem(this.selectedDate);
    }
  }
};
</script>

<style lang="scss" scoped>
.activity-calendar {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 16px;

  .header {
    position: relative;
    text-align: center;
    margin-bottom: 20px;

    .title {
      font-size: 24px;
      font-weight: bold;
      color: #fff;
      margin-bottom: 10px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .illustration {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 20px;
      position: relative;

      .calendar-icon {
        position: relative;
        width: 120px;
        height: 100px;

        .calendar-header {
          width: 80px;
          height: 20px;
          background: #fff;
          border-radius: 8px 8px 0 0;
          margin: 0 auto;
          position: relative;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

          &::before,
          &::after {
            content: "";
            position: absolute;
            top: -8px;
            width: 4px;
            height: 16px;
            background: #ff6b6b;
            border-radius: 2px;
          }

          &::before {
            left: 15px;
          }

          &::after {
            right: 15px;
          }
        }

        .calendar-body {
          width: 80px;
          height: 60px;
          background: #fff;
          border-radius: 0 0 8px 8px;
          margin: 0 auto;
          padding: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

          .calendar-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 4px;
            height: 100%;

            .grid-item {
              background: #f0f0f0;
              border-radius: 2px;
              transition: all 0.3s ease;

              &.active {
                background: linear-gradient(135deg, #667eea, #764ba2);
                transform: scale(1.1);
                box-shadow: 0 2px 4px rgba(102, 126, 234, 0.4);
              }
            }
          }
        }

        .floating-elements {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;

          .element {
            position: absolute;
            font-size: 20px;
            animation: float 3s ease-in-out infinite;

            &.element-1 {
              top: 10px;
              left: 10px;
              animation-delay: 0s;
            }

            &.element-2 {
              top: 20px;
              right: 10px;
              animation-delay: 1s;
            }

            &.element-3 {
              bottom: 10px;
              left: 20px;
              animation-delay: 2s;
            }
          }
        }
      }
    }

    @keyframes float {
      0%,
      100% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
    }
  }

  .calendar-container {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    margin-bottom: 20px;

    .custom-calendar {
      border-radius: 16px;

      // 自定义日历样式
      :deep(.van-calendar__header) {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        padding: 16px;

        .van-calendar__month-title {
          color: #fff;
          font-weight: bold;
        }
      }

      :deep(.van-calendar__weekdays) {
        background: #f8f9fa;

        .van-calendar__weekday {
          color: #666;
          font-weight: 500;
        }
      }

      :deep(.van-calendar__month) {
        background: #fff;
      }

      :deep(.van-calendar__day) {
        position: relative;

        &.has-event {
          .van-calendar__day-text {
            background: #fff;
            border-radius: 50%;
            position: relative;
            z-index: 2;
          }

          &.festival {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: #fff;
            border-radius: 8px;
          }

          &.activity {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: #fff;
            border-radius: 8px;
          }

          &.solar-term {
            background: linear-gradient(135deg, #feca57, #ff9ff3);
            color: #fff;
            border-radius: 8px;
          }

          &.training {
            background: linear-gradient(135deg, #48cae4, #0077b6);
            color: #fff;
            border-radius: 8px;
          }

          &.promotion {
            background: linear-gradient(135deg, #f72585, #b5179e);
            color: #fff;
            border-radius: 8px;
          }

          &.culture {
            background: linear-gradient(135deg, #7209b7, #560bad);
            color: #fff;
            border-radius: 8px;
          }
        }

        &.van-calendar__day--selected {
          background: linear-gradient(135deg, #667eea, #764ba2) !important;
          color: #fff !important;
          border-radius: 8px;

          .van-calendar__day-text {
            background: transparent;
            color: #fff;
          }
        }
      }

      :deep(.van-calendar__bottom-info) {
        font-size: 10px;
        margin-top: 2px;
        color: #fff;
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .timeline-container {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    max-height: 400px;
    overflow-y: auto;

    .timeline-title {
      padding: 16px 20px;
      font-size: 18px;
      font-weight: bold;
      color: #333;
      border-bottom: 1px solid #f0f0f0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
      border-radius: 16px 16px 0 0;
    }

    .timeline-content {
      padding: 20px;
    }

    .timeline-item {
      display: flex;
      margin-bottom: 24px;
      padding: 16px;
      border-radius: 12px;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:last-child {
        margin-bottom: 0;
      }

      &.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        border-color: #667eea;
        transform: translateX(8px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

        .timeline-date {
          .date-circle {
            background: #fff;
            color: #667eea;
          }

          .date-text {
            color: #fff;
          }
        }

        .event-title {
          color: #fff;
        }

        .event-description {
          color: rgba(255, 255, 255, 0.9);
        }

        .event-tag {
          background: rgba(255, 255, 255, 0.2);
          color: #fff;
          border-color: rgba(255, 255, 255, 0.3);
        }
      }

      .timeline-date {
        flex-shrink: 0;
        margin-right: 16px;
        text-align: center;

        .date-circle {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea, #764ba2);
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 16px;
          margin-bottom: 8px;
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .date-text {
          font-size: 12px;
          color: #666;
          font-weight: 500;
        }
      }

      .timeline-content-item {
        flex: 1;

        .event-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }

        .event-description {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          margin-bottom: 12px;
        }

        .event-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .event-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid;
            transition: all 0.3s ease;

            &.festival {
              background: rgba(255, 107, 107, 0.1);
              color: #ff6b6b;
              border-color: #ff6b6b;
            }

            &.activity {
              background: rgba(78, 205, 196, 0.1);
              color: #4ecdc4;
              border-color: #4ecdc4;
            }

            &.solar-term {
              background: rgba(254, 202, 87, 0.1);
              color: #feca57;
              border-color: #feca57;
            }

            &.training {
              background: rgba(72, 202, 228, 0.1);
              color: #48cae4;
              border-color: #48cae4;
            }

            &.business {
              background: rgba(0, 119, 182, 0.1);
              color: #0077b6;
              border-color: #0077b6;
            }

            &.promotion {
              background: rgba(247, 37, 133, 0.1);
              color: #f72585;
              border-color: #f72585;
            }

            &.culture {
              background: rgba(114, 9, 183, 0.1);
              color: #7209b7;
              border-color: #7209b7;
            }

            &.community {
              background: rgba(86, 11, 173, 0.1);
              color: #560bad;
              border-color: #560bad;
            }
          }
        }
      }
    }
  }
}

// 滚动条样式
.timeline-container::-webkit-scrollbar {
  width: 6px;
}

.timeline-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.timeline-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

.timeline-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}
</style>

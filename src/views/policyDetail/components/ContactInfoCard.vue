<template>
  <div class="contact-info-card">
    <div class="card-header">
      <div class="header-line"></div>
      <h3 class="card-title">{{ contact.title }}</h3>
    </div>

    <div class="card-content">
      <div class="contact-item">
        <span class="contact-label">联系人:</span>
        <span class="contact-value">{{ contact.contactPerson }}</span>
      </div>

      <div class="contact-item">
        <span class="contact-label">联系方式:</span>
        <span class="contact-value phone-number" @click="makeCall">
          {{ contact.contactMethod }}
        </span>
        <van-icon name="phone-o" class="phone-icon" @click="makeCall" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ContactInfoCard",
  props: {
    contact: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  methods: {
    makeCall() {
      // 拨打电话
      window.location.href = `tel:${this.contact.contactMethod}`;
    }
  }
};
</script>

<style lang="scss" scoped>
.contact-info-card {
  margin: 12px 16px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  .card-header {
    padding: 16px 16px 0;
    display: flex;
    align-items: center;

    .header-line {
      width: 3px;
      height: 16px;
      background-color: #2bc472;
      border-radius: 2px;
      margin-right: 8px;
    }

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      margin: 0;
    }
  }

  .card-content {
    padding: 12px 16px 16px;

    .contact-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .contact-label {
        font-size: 14px;
        color: #646566;
        min-width: 70px;
      }

      .contact-value {
        font-size: 14px;
        color: #323233;
        flex: 1;

        &.phone-number {
          //   color: #2bc472;
          cursor: pointer;

          &:active {
            opacity: 0.7;
          }
        }
      }

      .phone-icon {
        // color: #2bc472;
        font-size: 16px;
        margin-left: 8px;
        cursor: pointer;

        &:active {
          opacity: 0.7;
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .contact-info-card {
    margin: 8px 12px;

    .card-header {
      padding: 14px 14px 0;

      .card-title {
        font-size: 15px;
      }
    }

    .card-content {
      padding: 10px 14px 14px;

      .contact-item {
        .contact-label,
        .contact-value {
          font-size: 13px;
        }

        .contact-label {
          min-width: 65px;
        }
      }
    }
  }
}
</style>

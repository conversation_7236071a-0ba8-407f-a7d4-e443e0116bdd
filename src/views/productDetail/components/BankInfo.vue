<template>
  <div class="bank-info">
    <div class="bank-content">
      <!-- <img :src="bank.logo" :alt="bank.name" class="bank-logo" /> -->
      <div class="bank-details">
        <span class="bank-short-name">{{ bank.cpmc }}</span>
        <span class="bank-full-name">{{ bank.sftycp }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "BankInfo",
  props: {
    bank: {
      type: Object,
      required: true
    }
  }
};
</script>

<style lang="scss" scoped>
.bank-info {
  background-color: white;
  padding: 16px;
  margin-bottom: 8px;

  .bank-content {
    display: flex;
    align-items: center;

    .bank-logo {
      width: 32px;
      height: 32px;
      border-radius: 6px;
      margin-right: 12px;
    }

    .bank-details {
      display: flex;
      padding: 0 12px;
      width: 100%;
      align-items: center;
      justify-content: space-between;

      .bank-short-name {
        font-weight: 500;
        font-size: 16px;
      }

      .bank-full-name {
        font-size: 16px;
        color: #646566;
        font-size: 14px;
      }
    }
  }
}
</style>

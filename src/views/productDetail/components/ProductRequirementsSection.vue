<template>
  <div class="product-requirements-section">
    <h3 class="section-title">申请条件</h3>
    <div class="requirements-list">
      <div
        v-for="(requirement, index) in requirements"
        :key="index"
        class="requirement-item"
      >
        <div class="requirement-number">{{ index + 1 }}</div>
        <div class="requirement-text">{{ requirement }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProductRequirementsSection",
  props: {
    requirements: {
      type: Array,
      required: true
    }
  }
};
</script>

<style lang="scss" scoped>
.product-requirements-section {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin: 0 0 16px 0;
  }

  .requirements-list {
    .requirement-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .requirement-number {
        width: 20px;
        height: 20px;
        background-color: #1989fa;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 600;
        margin-right: 12px;
        flex-shrink: 0;
        margin-top: 2px;
      }

      .requirement-text {
        font-size: 14px;
        color: #646566;
        line-height: 1.5;
        flex: 1;
      }
    }
  }
}
</style>

<template>
  <div class="product-info-section">
    <h3 class="section-title">{{ title }}</h3>
    <div class="section-content">{{ content }}</div>
  </div>
</template>

<script>
export default {
  name: "ProductInfoSection",
  props: {
    title: {
      type: String,
      required: true
    },
    content: {
      type: String,
      required: true
    }
  }
};
</script>

<style lang="scss" scoped>
.product-info-section {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin: 0 0 12px 0;
  }

  .section-content {
    font-size: 14px;
    color: #646566;
    line-height: 1.5;
  }
}
</style>

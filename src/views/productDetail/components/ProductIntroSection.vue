<template>
  <div class="product-intro-section">
    <div class="section-header">
      <h3 class="section-title">{{ introduction.title }}</h3>
    </div>
    
    <div class="intro-content">
      <div class="intro-highlight">
        <div class="highlight-bar"></div>
        <h4 class="highlight-title">{{ introduction.title }}</h4>
      </div>
      
      <div class="intro-text">
        {{ introduction.content }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductIntroSection',
  props: {
    introduction: {
      type: Object,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.product-intro-section {
  background-color: white;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  
  .section-header {
    padding: 16px 16px 0;
    
    .section-title {
      font-size: 16px;
      color: #646566;
      margin: 0;
      font-weight: 400;
    }
  }
  
  .intro-content {
    padding: 12px 16px 16px;
    
    .intro-highlight {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      
      .highlight-bar {
        width: 3px;
        height: 16px;
        background-color: #1989fa;
        border-radius: 2px;
        margin-right: 8px;
      }
      
      .highlight-title {
        font-size: 16px;
        font-weight: 600;
        color: #323233;
        margin: 0;
      }
    }
    
    .intro-text {
      background-color: #f7f8fa;
      padding: 16px;
      border-radius: 8px;
      font-size: 14px;
      line-height: 1.6;
      color: #646566;
      text-align: justify;
    }
  }
}
</style>

<template>
  <div class="product-header">
    <div class="header-content">
      <div class="product-stats">
        <div class="stat-item">
          <div class="stat-label">贷款额度</div>
          <div class="stat-value">
            {{ product.dkedq }}-{{ product.dkedz }}万
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-label">参考利率</div>
          <div class="stat-value">
            {{ product.cklvq ? product.cklvq.toFixed(2) : "-" }}%-{{
              product.cklvz ? product.cklvz.toFixed(2) : "-"
            }}%
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProductHeader",
  props: {
    product: {
      type: Object,
      required: true
    }
  }
};
</script>

<style lang="scss" scoped>
.product-header {
  background: linear-gradient(135deg, #0080ec 0%, #0080ec 100%);
  color: white;

  .header-content {
    padding: 34px 16px;

    .product-stats {
      display: flex;
      justify-content: space-around;

      .stat-item {
        text-align: center;

        .stat-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.9);
          margin-bottom: 24px;
        }

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: white;
        }
      }
    }
  }
}
</style>

<template>
  <div class="requirement-search">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-container">
        <van-search
          v-model="searchValue"
          placeholder="请输入关键词搜索"
          shape="round"
          background="transparent"
          @search="onSearch"
          class="search-input"
        />
        <van-button
          type="primary"
          round
          size="small"
          class="post-button"
          @click="postRequirement"
        >
          发需求
        </van-button>
      </div>
    </div>

    <!-- 筛选标签栏 -->
    <div class="filter-tabs">
      <div class="result-count">为您找到 {{ totalCount }} 条需求</div>
      <div class="filter-options">
        <div
          v-for="(tab, index) in filterTabs"
          :key="index"
          class="filter-tab"
          :class="{ active: activeTab === tab.value }"
          @click="selectTab(tab.value)"
        >
          {{ tab.label }}
        </div>
      </div>
    </div>

    <!-- 需求列表 -->
    <div class="requirement-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          loading-text="加载中..."
          @load="onLoadMore"
        >
          <div
            v-for="(item, index) in requirementList"
            :key="index"
            class="requirement-card"
          >
            <!-- 需求标题和公司 -->
            <div class="card-header">
              <h3 class="requirement-title">{{ item.title }}</h3>
              <div class="company-name">{{ item.companyName }}</div>
            </div>

            <!-- 标签 -->
            <div class="tags-section">
              <span
                v-for="(tag, tagIndex) in item.tags"
                :key="tagIndex"
                class="requirement-tag"
                :class="getTagClass(tag)"
              >
                {{ tag }}
              </span>
            </div>

            <!-- 需求描述 -->
            <div class="requirement-description">
              {{ item.description }}
            </div>

            <!-- 联系信息和操作 -->
            <div class="card-footer">
              <div class="contact-info">
                <div class="contact-person">
                  <van-icon name="contact" class="contact-icon" />
                  <span class="person-name">{{ item.contactPerson }}</span>
                  <span class="phone-number">{{ item.phoneNumber }}</span>
                </div>
                <div class="time-info">
                  <div class="publish-time">
                    发布时间: {{ item.publishTime }}
                  </div>
                  <div class="expire-time">有效期至{{ item.expireTime }}</div>
                </div>
              </div>
              <van-button
                type="primary"
                size="small"
                round
                class="detail-button"
                @click="viewDetail(item)"
              >
                查看详情
              </van-button>
            </div>
          </div>

          <!-- 空状态 -->
          <van-empty
            v-if="!loading && requirementList.length === 0"
            description="暂无相关需求"
            image="search"
          />
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
export default {
  name: "RequirementSearch",
  data() {
    return {
      searchValue: "",
      activeTab: "latest",
      totalCount: 24,
      refreshing: false,
      loading: false,
      finished: false,
      pageNum: 1,
      pageSize: 10,

      filterTabs: [
        { label: "最新发布", value: "latest" },
        { label: "即将到期", value: "expiring" },
        { label: "筛选", value: "filter" }
      ],

      requirementList: []
    };
  },

  mounted() {
    this.loadRequirements();
  },

  methods: {
    // 生成模拟数据
    generateMockData() {
      const mockRequirements = [];
      const titles = [
        "短视频代运营",
        "网络营销推广",
        "电商运营",
        "品牌策划",
        "内容创作"
      ];
      const companies = [
        "烟台云商来数据科技有限公司",
        "青岛互联网科技有限公司",
        "济南数字营销公司",
        "威海创新科技企业",
        "潍坊电商服务公司"
      ];
      const contacts = ["田培新", "李经理", "王总", "张主管", "刘总监"];
      const phones = [
        "15615551807",
        "13812345678",
        "18765432109",
        "15987654321",
        "13698765432"
      ];

      for (let i = 0; i < this.pageSize; i++) {
        const index = (this.pageNum - 1) * this.pageSize + i;
        if (index >= this.totalCount) break;

        mockRequirements.push({
          id: index + 1,
          title: titles[index % titles.length],
          companyName: companies[index % companies.length],
          tags: ["支票收", "培训机构", "其他", "软件和信息服务业"],
          description: "短视频代运营，短视频招聘营销运营管理",
          contactPerson: contacts[index % contacts.length],
          phoneNumber: phones[index % phones.length],
          publishTime: "2024-11-29",
          expireTime: "2024-11-29"
        });
      }

      return mockRequirements;
    },

    async loadRequirements() {
      if (this.loading || this.finished) return;

      this.loading = true;

      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));

        const newData = this.generateMockData();

        if (this.pageNum === 1) {
          this.requirementList = newData;
        } else {
          this.requirementList = [...this.requirementList, ...newData];
        }

        // 判断是否加载完成
        if (
          this.requirementList.length >= this.totalCount ||
          newData.length === 0
        ) {
          this.finished = true;
        }
      } catch (error) {
        console.error("加载需求列表失败:", error);
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },

    onRefresh() {
      this.pageNum = 1;
      this.finished = false;
      this.requirementList = [];
      this.loadRequirements();
    },

    onLoadMore() {
      this.pageNum++;
      this.loadRequirements();
    },

    onSearch(value) {
      console.log("搜索:", value);
      this.onRefresh();
    },

    selectTab(value) {
      this.activeTab = value;
      console.log("选择标签:", value);
      this.onRefresh();
    },

    postRequirement() {
      console.log("发布需求");
      // 跳转到发布需求页面
    },

    viewDetail(item) {
      console.log("查看详情:", item);
      // 跳转到需求详情页面
    },

    getTagClass(tag) {
      const tagClassMap = {
        支票收: "tag-primary",
        培训机构: "tag-success",
        其他: "tag-warning",
        软件和信息服务业: "tag-info"
      };
      return tagClassMap[tag] || "tag-default";
    }
  }
};
</script>

<style lang="scss" scoped>
.requirement-search {
  min-height: 100vh;
  background-color: #f7f8fa;

  .search-section {
    background-color: white;
    padding: 12px 16px;

    .search-container {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-input {
        flex: 1;

        :deep(.van-search) {
          padding: 0;

          .van-search__content {
            background-color: #f7f8fa;
            border-radius: 20px;
          }

          .van-field__control {
            font-size: 14px;
          }
        }
      }

      .post-button {
        height: 32px;
        padding: 0 16px;
        font-size: 13px;
        white-space: nowrap;
      }
    }
  }

  .filter-tabs {
    background-color: white;
    padding: 12px 16px;
    border-bottom: 1px solid #ebedf0;

    .result-count {
      font-size: 14px;
      color: #646566;
      margin-bottom: 12px;
    }

    .filter-options {
      display: flex;
      gap: 16px;

      .filter-tab {
        font-size: 14px;
        color: #646566;
        cursor: pointer;
        padding: 4px 0;
        position: relative;
        transition: color 0.2s;

        &.active {
          color: #1989fa;
          font-weight: 500;

          &::after {
            content: "";
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background-color: #1989fa;
            border-radius: 1px;
          }
        }
      }
    }
  }

  .requirement-list {
    padding: 12px 16px;

    .requirement-card {
      background-color: white;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        .requirement-title {
          font-size: 16px;
          font-weight: 600;
          color: #323233;
          margin: 0;
          flex: 1;
        }

        .company-name {
          font-size: 12px;
          color: #646566;
          margin-left: 12px;
          white-space: nowrap;
        }
      }

      .tags-section {
        margin-bottom: 12px;

        .requirement-tag {
          display: inline-block;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 11px;
          margin-right: 8px;
          margin-bottom: 4px;

          &.tag-primary {
            background-color: rgba(25, 137, 250, 0.1);
            color: #1989fa;
          }

          &.tag-success {
            background-color: rgba(7, 193, 96, 0.1);
            color: #07c160;
          }

          &.tag-warning {
            background-color: rgba(255, 149, 0, 0.1);
            color: #ff9500;
          }

          &.tag-info {
            background-color: rgba(238, 10, 36, 0.1);
            color: #ee0a24;
          }

          &.tag-default {
            background-color: #f7f8fa;
            color: #646566;
          }
        }
      }

      .requirement-description {
        font-size: 14px;
        color: #646566;
        line-height: 1.5;
        margin-bottom: 16px;
      }

      .card-footer {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .contact-info {
          flex: 1;

          .contact-person {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .contact-icon {
              color: #1989fa;
              font-size: 14px;
              margin-right: 4px;
            }

            .person-name {
              font-size: 14px;
              color: #323233;
              margin-right: 8px;
            }

            .phone-number {
              font-size: 14px;
              color: #646566;
            }
          }

          .time-info {
            font-size: 12px;
            color: #969799;

            .publish-time {
              margin-bottom: 2px;
            }
          }
        }

        .detail-button {
          height: 32px;
          padding: 0 16px;
          font-size: 13px;
          margin-left: 12px;
        }
      }
    }

    :deep(.van-empty) {
      padding: 60px 0;
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .requirement-search {
    .search-section {
      padding: 8px 12px;

      .search-container {
        gap: 8px;

        .post-button {
          height: 28px;
          padding: 0 12px;
          font-size: 12px;
        }
      }
    }

    .filter-tabs {
      padding: 8px 12px;

      .filter-options {
        gap: 12px;

        .filter-tab {
          font-size: 13px;
        }
      }
    }

    .requirement-list {
      padding: 8px 12px;

      .requirement-card {
        padding: 12px;

        .card-header {
          .requirement-title {
            font-size: 15px;
          }

          .company-name {
            font-size: 11px;
          }
        }

        .requirement-description {
          font-size: 13px;
        }

        .card-footer {
          .contact-info {
            .contact-person {
              .person-name,
              .phone-number {
                font-size: 13px;
              }
            }

            .time-info {
              font-size: 11px;
            }
          }

          .detail-button {
            height: 28px;
            padding: 0 12px;
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>

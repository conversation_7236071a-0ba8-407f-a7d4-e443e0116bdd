<template>
  <div class="institution-detail">
    <!-- 头部导航 -->
    <van-nav-bar
      title="机构详情"
      left-arrow
      @click-left="onClickLeft"
      class="custom-nav-bar"
    >
      <template #right>
        <van-icon
          name="ellipsis"
          class="nav-menu"
          @click="showActionSheet = true"
        />
      </template>
    </van-nav-bar>

    <!-- 机构统计卡片 -->
    <institution-stats-card :stats="institutionStats" />

    <!-- 机构介绍 -->
    <institution-intro-card :introduction="institutionIntro" />

    <!-- 联系方式 -->
    <contact-info-card :contact="contactInfo" />

    <!-- 操作菜单 -->
    <van-action-sheet
      v-model="showActionSheet"
      :actions="actionSheetActions"
      @select="onActionSelect"
      cancel-text="取消"
    />
  </div>
</template>

<script>
import InstitutionStatsCard from "./components/InstitutionStatsCard.vue";
import InstitutionIntroCard from "./components/InstitutionIntroCard.vue";
import ContactInfoCard from "./components/ContactInfoCard.vue";

export default {
  name: "InstitutionDetail",
  components: {
    InstitutionStatsCard,
    InstitutionIntroCard,
    ContactInfoCard
  },
  data() {
    return {
      showActionSheet: false,
      institutionStats: {
        name: "青岛农商银行",
        logo: "/placeholder.svg?height=48&width=48&text=农商",
        productCount: 8,
        financingAmount: "100万",
        productLabel: "发布金融产品",
        financingLabel: "这款融资"
      },
      institutionIntro: {
        title: "机构介绍",
        content: `青岛农商银行作为青岛本土农村合作银行，青岛城阳农村合作银行，青岛莱西农村合作银行，青岛即墨农村合作银行，青岛莱西农村合作银行和胶南、胶州、平度、农村信用联社及青岛农村信用合作社联合社基础上，以新设合并方式发起设立的股份制商业银行，是青岛第一家在全国全市农村信用社基础上组建的农村商业银行。2019年3月26日，青岛农商银行首次公开发行A股在深圳证券交易所挂牌上市，证券代码002958，股票简称"青岛银行"。`
      },
      contactInfo: {
        title: "联系方式",
        contactPerson: "王经理",
        contactMethod: "18736384758"
      },
      actionSheetActions: [
        { name: "分享机构", value: "share" },
        { name: "收藏机构", value: "favorite" },
        { name: "举报", value: "report" }
      ]
    };
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1);
    },
    onActionSelect(action) {
      console.log("选择操作:", action);
      switch (action.value) {
        case "share":
          this.shareInstitution();
          break;
        case "favorite":
          this.toggleFavorite();
          break;
        case "report":
          this.reportInstitution();
          break;
      }
    },
    shareInstitution() {
      // 实现分享功能
      this.$toast("分享功能");
    },
    toggleFavorite() {
      // 实现收藏功能
      this.$toast("收藏功能");
    },
    reportInstitution() {
      // 实现举报功能
      this.$toast("举报功能");
    }
  }
};
</script>

<style lang="scss" scoped>
.institution-detail {
  min-height: 100vh;
  background-color: #f7f8fa;

  .custom-nav-bar {
    background-color: white;

    :deep(.van-nav-bar__title) {
      color: #323233;
      font-weight: 500;
    }

    .nav-menu {
      color: #969799;
      font-size: 18px;
    }
  }
}
</style>

<template>
  <div class="homepage-header">
    <div class="header-background">
      <!-- <div class="city-skyline"></div> -->
    </div>

    <div class="header-content">
      <!-- 标题 -->
      <div class="header-title">
        <img src="~@/assets/images/home/<USER>" alt="" />
      </div>

      <!-- 统计数据 -->
      <div class="stats-container">
        <div v-for="(stat, index) in statistics" :key="index" class="stat-item">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">
            {{ stat.label }}
            <span class="unit">({{ stat.unit }})</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "HomepageHeader",
  props: {
    statistics: {
      type: Array,
      required: true,
      default: () => []
    }
  }
};
</script>

<style lang="scss" scoped>
.homepage-header {
  position: relative;
  height: 260px;
  background: url("~@/assets/images/home/<USER>") no-repeat center bottom;
  background-size: cover;
  overflow: hidden;

  .header-background {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 120px;

    .city-skyline {
      width: 100%;
      height: 100%;
      background: linear-gradient(
        to top,
        rgba(0, 0, 0, 0.1) 0%,
        transparent 100%
      );
    }
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 0 16px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .status-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      color: white;

      .time {
        font-size: 16px;
        font-weight: 600;
      }

      .status-icons {
        display: flex;
        gap: 8px;

        .van-icon {
          font-size: 18px;
        }
      }
    }

    .header-title {
      text-align: center;
      margin: 60px 0 0px;
      img {
        width: 212px;
        object-fit: contain;
      }

      .main-title {
        font-size: 24px;
        font-weight: 700;
        color: white;
        margin: 0 0 4px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .sub-title {
        font-size: 24px;
        font-weight: 700;
        color: white;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }

    .stats-container {
      display: flex;
      justify-content: space-around;
      align-items: center;
      flex: 1;

      .stat-item {
        text-align: center;
        color: white;

        .stat-value {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 4px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-label {
          font-size: 12px;
          opacity: 0.9;
        }
      }
    }
  }
}
</style>

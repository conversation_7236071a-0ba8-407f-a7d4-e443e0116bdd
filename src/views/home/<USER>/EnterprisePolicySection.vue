<template>
  <div class="enterprise-policy-section">
    <div class="section-header">
      <div class="section-title">
        <van-icon name="award-o" />
        <span>惠企政策</span>
      </div>
      <van-button type="primary" size="small" round>
        立即查看
      </van-button>
    </div>
    
    <div class="section-subtitle">
      政策解读——政策申报
    </div>
    
    <div class="policy-tags">
      <div 
        v-for="(policy, index) in policies" 
        :key="index"
        class="policy-tag"
        :class="{ active: index === 0 }"
      >
        {{ policy }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnterprisePolicySection',
  props: {
    policies: {
      type: Array,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.enterprise-policy-section {
  background-color: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  padding: 16px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      
      .van-icon {
        margin-right: 6px;
        color: #1989fa;
      }
    }
    
    :deep(.van-button) {
      height: 28px;
      padding: 0 12px;
      font-size: 12px;
    }
  }
  
  .section-subtitle {
    font-size: 13px;
    color: #646566;
    margin-bottom: 12px;
  }
  
  .policy-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    
    .policy-tag {
      padding: 6px 12px;
      border: 1px solid #ebedf0;
      border-radius: 16px;
      font-size: 12px;
      color: #646566;
      background-color: white;
      cursor: pointer;
      transition: all 0.2s;
      
      &.active {
        background-color: #1989fa;
        border-color: #1989fa;
        color: white;
      }
      
      &:hover:not(.active) {
        border-color: #1989fa;
        color: #1989fa;
      }
    }
  }
}
</style>

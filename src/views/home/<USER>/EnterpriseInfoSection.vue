<template>
  <div class="enterprise-info-section">
    <div class="section-header">
      <div class="section-title">
        <span>{{ title }}</span>
      </div>
      <van-button type="primary" size="small" round>
        立即查询
      </van-button>
    </div>

    <span
      class="section-subtitle"
      :style="{ color: subTitleColor, backgroundColor: subTitleBgColor }"
    >
      {{ desc }}
    </span>

    <div class="info-tags">
      <div class="info-tag" v-for="(item, i) in tagsList" :key="i">
        <van-icon name="user-o" />
        <span>{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "EnterpriseInfoSection",
  props: {
    subTitleColor: {
      type: String,
      default: "#bc9531"
    },
    subTitleBgColor: {
      type: String,
      default: "#ffe8c3"
    },
    tagsList: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: "一分钟知企业"
    },
    desc: {
      type: String,
      default: "查询企业基本信息一键搞定"
    }
  }
};
</script>

<style lang="scss" scoped>
.enterprise-info-section {
  background-color: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  padding: 16px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #323233;

      .van-icon {
        margin-right: 6px;
        color: #1989fa;
      }
    }

    :deep(.van-button) {
      height: 28px;
      padding: 0 12px;
      font-size: 12px;
    }
  }

  .section-subtitle {
    font-size: 13px;
    display: inline-block;
    color: #bc9531;
    margin-bottom: 12px;
    padding: 4px;
    background-color: #ffe8c3;
  }

  .info-tags {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    background-color: #f7f8fa;

    .info-tag {
      display: flex;
      align-items: center;
      padding: 4px;
      background-color: #f7f8fa;
      border-radius: 12px;
      font-size: 12px;
      color: #646566;

      .van-icon {
        margin-right: 4px;
        font-size: 12px;
      }
    }
  }
}
</style>

<template>
  <div class="service-menu">
    <div class="menu-container">
      <div
        v-for="(service, index) in services"
        :key="index"
        class="service-item"
        @click="navigateToService(service)"
      >
        <div class="service-icon">
          <img :src="service.icon" />
        </div>
        <div class="service-title">{{ service.title }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ServiceMenu",
  props: {
    services: {
      type: Array,
      required: true
    }
  },
  methods: {
    navigateToService(service) {
      this.$router.push(service.route);
    }
  }
};
</script>

<style lang="scss" scoped>
.service-menu {
  background-color: white;
  margin: -20px 16px 16px;
  border-radius: 12px;
  // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 3;

  .menu-container {
    display: flex;
    padding: 12px;

    .service-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      .service-icon {
        width: 34px;
        height: 34px;
        margin-bottom: 8px;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .service-title {
        font-size: 13px;
        color: #666;
        text-align: center;
        font-weight: 500;
      }
    }
  }
}
</style>

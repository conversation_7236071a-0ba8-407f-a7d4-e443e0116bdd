<template>
  <div class="homepage">
    <!-- 头部统计区域 -->
    <homepage-header :statistics="hotTotal" />

    <!-- 主要服务入口 -->
    <service-menu :services="mainServices" />

    <!-- 一分钟知企业 -->
    <enterprise-info-section :tagsList="enterpriseTags" />
    <enterprise-info-section
      title="惠企政策"
      desc="高效赋能、一站直达"
      subTitleColor="#3185BC"
      subTitleBgColor="#C3DEFF"
      :tagsList="policyTags"
    />

    <!-- 惠企政策 -->
    <!-- <enterprise-policy-section :policies="policyTags" /> -->

    <!-- 非法集资举报 -->
    <report-section />

    <!-- 热门产品 -->
    <hot-products-section :products="hotProducts" />
  </div>
</template>

<script>
import HomepageHeader from "./components/HomepageHeader.vue";
import ServiceMenu from "./components/ServiceMenu.vue";
import EnterpriseInfoSection from "./components/EnterpriseInfoSection.vue";
import EnterprisePolicySection from "./components/EnterprisePolicySection.vue";
import ReportSection from "./components/ReportSection.vue";
import HotProductsSection from "./components/HotProductsSection.vue";
import { homeHotData, homeHotTotal } from "@/api/home";
export default {
  name: "Homepage",
  components: {
    HomepageHeader,
    ServiceMenu,
    EnterpriseInfoSection,
    EnterprisePolicySection,
    ReportSection,
    HotProductsSection
  },
  data() {
    return {
      hotProducts: [],
      hotTotal: [
        { label: "产品数", unit: "个", value: "", key: "jrcpCount" },
        { label: "融资需求", unit: "笔", value: "", key: "rzdtCount" },
        { label: "融资金额", unit: "万元", value: "", key: "rzjeCount" }
      ],
      enterpriseTags: [
        { name: "工商信息" },
        { name: "风险信息" },
        { name: "商机线索" },
        { name: "对外投资" }
      ],
      policyTags: [
        { name: "政策要点解析" },
        { name: "政策动态预警" },
        { name: "政策动态预警" }
      ],
      headerStats: {
        title: "烟台市融资信用",
        subtitle: "服务平台",
        stats: [
          { value: "56", label: "累计数量(个)" },
          { value: "156", label: "累计数量(笔)" },
          { value: "8232.1", label: "融资总额(万元)" }
        ]
      },
      mainServices: [
        {
          icon: require("@/assets/images/home/<USER>"),
          title: "我要融资",
          route: "/publish"
        },
        {
          icon: require("@/assets/images/home/<USER>"),
          title: "金融超市",
          route: "/productList"
        },
        {
          icon: require("@/assets/images/home/<USER>"),
          title: "金融机构",
          route: "/orgList"
        },
        {
          icon: require("@/assets/images/home/<USER>"),
          title: "智能匹配",
          route: "/smart-match"
        }
      ]

      // hotProducts: [
      //   {
      //     id: 1,
      //     name: "人才贷",
      //     amount: "100",
      //     description: "最高可申请额度(万元)",
      //     bankName: "青岛银行",
      //     bankLogo: "/placeholder.svg?height=24&width=24&text=青行"
      //   },
      //   {
      //     id: 2,
      //     name: "信用贷",
      //     amount: "100",
      //     description: "最高可申请额度(万元)",
      //     bankName: "烟台银行",
      //     bankLogo: "/placeholder.svg?height=24&width=24&text=烟行"
      //   },
      //   {
      //     id: 3,
      //     name: "小额贷",
      //     amount: "100",
      //     description: "最高可申请额度(万元)",
      //     bankName: "光大银行",
      //     bankLogo: "/placeholder.svg?height=24&width=24&text=光大"
      //   }
      // ]
    };
  },
  created() {
    this._homeHotData();
    this._homeHotTotal();
  },
  methods: {
    _homeHotTotal() {
      homeHotTotal().then(res => {
        this.hotTotal.forEach(item => {
          item.value = res[item.key]
            ? // ? (Number(res[item.key]) / 10000).toFixed(2)
              item.key !== "rzjeCount"
              ? res[item.key]
              : (res[item.key] / 10000).toFixed(2)
            : "";
        });
      });
    },
    _homeHotData() {
      homeHotData().then(res => {
        console.log(res);
        this.hotProducts = res;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.homepage {
  height: 100%;
  background-color: #f7f8fa;
  // padding-bottom: 20px;
}
</style>

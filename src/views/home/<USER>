.page {
    background-color: rgba(245, 245, 245, 1);
    position: relative;
    width: 100%;
    // height: 1207px;
    overflow: hidden;
    .group_1 {
      background: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9c662efbe4744be6bad3c8afecfcbec3_mergeImage.png)
        100% no-repeat;
      background-size: 100% 100%;
      padding: 13px 10px 78px 10px;
      .image_1 {
        width: 354px;
        height: 12px;
        margin-right: 1px;
      }
      .image_2 {
        width: 86px;
        height: 32px;
        margin: 16px 0 0 269px;
      }
      .text_1 {
        text-shadow: 0px 2px 1px rgba(0, 0, 0, 0.73);
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 30px;
        font-family: ZhenyanGB-Regular;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 31px;
        align-self: center;
        margin-top: 16px;
      }
      .text_2 {
        text-shadow: 0px 2px 1px rgba(0, 0, 0, 0.73);
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 32px;
        font-family: ZhenyanGB-Regular;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 33px;
        margin: 11px 115px 0 112px;
      }
      .box_1 {
        width: 310px;
        margin: 16px 24px 0 21px;
        .list_1 {
          width: 175px;
          height: 49px;
          justify-content: space-between;
          .text-group_1 {
            margin-right: 51px;
            .text_3 {
              width: 33px;
              overflow-wrap: break-word;
              color: rgba(252, 252, 252, 1);
              font-size: 20px;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 28px;
              align-self: center;
            }
            .text_4 {
              width: 68px;
              overflow-wrap: break-word;
              color: rgba(252, 252, 252, 1);
              font-size: 12px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 17px;
              margin-top: 4px;
            }
          }
        }
        .text-group_2 {
          .text_5 {
            overflow-wrap: break-word;
            color: rgba(252, 252, 252, 1);
            font-size: 20px;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 28px;
            align-self: center;
          }
          .text_6 {
            overflow-wrap: break-word;
            color: rgba(252, 252, 252, 1);
            font-size: 12px;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 17px;
            margin-top: 4px;
          }
        }
      }
    }
    .group_2 {
      background-color: rgba(255, 255, 255, 1);
      margin-top: 231px;
      padding-top: 10px;
      .box_2 {
        background-color: rgba(241, 245, 255, 1);
        border-radius: 8px;
        position: relative;
        margin: 0 10px 0 9px;
        padding: 12px 171px 11px 91px;
        .text-wrapper_1 {
          .text_7 {
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 16px;
            letter-spacing: -0.38588234782218933px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
          }
          .text_8 {
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 11px;
            letter-spacing: -0.2652941048145294px;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 16px;
            margin: 1px 8px 0 0;
          }
        }
        .image-wrapper_1 {
          height: 45px;
          background: url(https://lanhu-oss-2537-2.lanhuapp.com/SketchPng0e567ce8729c53a6c2b1aea3bb37c50741daee504aa8367d12f6f5dd733fc5ea) -1px
            0px no-repeat;
          background-size: 63px 45px;
          width: 62px;
          position: absolute;
          left: 34px;
          top: 17px;
          .label_1 {
            width: 33px;
            height: 33px;
            margin: -4px 38px 16px -9px;
          }
        }
      }
      .box_3 {
        width: 332px;
        margin: 12px 23px 0 20px;
        .image-text_1 {
          width: 86px;
          .thumbnail_1 {
            width: 15px;
            height: 16px;
            margin: 4px 0 2px 0;
          }
          .text-group_3 {
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 16px;
            font-family: PingFangSC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 22px;
          }
        }
        .image-text_2 {
          width: 63px;
          margin: 2px 0 3px 0;
          .text-group_4 {
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 12px;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 17px;
          }
          .thumbnail_2 {
            width: 7px;
            height: 12px;
            margin: 3px 0 2px 0;
          }
        }
      }
      .list_2 {
        height: 459px;
        margin: 12px 21px 0 20px;
        .list-items_1 {
          width: 334px;
          background: url(https://lanhu-oss-2537-2.lanhuapp.com/SketchPng8a383bfb03563e70fa90cdf349a08df9270f0c44f0b87a3f6f4b186450abf020)
            100% no-repeat;
          background-size: 100% 100%;
          margin-bottom: 12px;
          padding: 16px 0 0 18px;
          .group_3 {
            margin-bottom: 14px;
            .text_9 {
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 16px;
              font-family: PingFangSC-Semibold;
              font-weight: 600;
              text-align: left;
              white-space: nowrap;
              line-height: 22px;
              margin-right: 97px;
            }
            .text_10 {
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 12px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 17px;
              margin-top: 3px;
            }
            .text-wrapper_2 {
              width: 88px;
              height: 45px;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 45px;
              margin: 10px 57px 0 0;
              .text_11 {
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 32px;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 45px;
              }
              .text_12 {
                overflow-wrap: break-word;
                color: rgba(51, 51, 51, 1);
                font-size: 18px;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                line-height: 45px;
              }
            }
            .text_13 {
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 12px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 17px;
              margin: 1px 53px 0 0;
            }
          }
          .group_4 {
            background: url(https://lanhu-oss-2537-2.lanhuapp.com/SketchPnge44ff99fcaa838e74896ef33c95aac385f371b3e336e71f92237f433bca247eb) -1px
              0px no-repeat;
            background-size: 184px 115px;
            position: relative;
            margin: 14px 0 0 -12px;
            padding: 60px 15px 21px 78px;
            .text-wrapper_3 {
              background: url(https://lanhu-oss-2537-2.lanhuapp.com/SketchPng2f391a728c3ec644c9fae7ff445213b204179f2d014b58126874410f97839af5) -4px -2px
                no-repeat;
              background-size: 98px 42px;
              padding: 7px 17px 7px 17px;
              .text_14 {
                overflow-wrap: break-word;
                color: rgba(255, 255, 255, 1);
                font-size: 14px;
                font-family: PingFangSC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 20px;
              }
            }
            .image_3 {
              position: absolute;
              left: 112px;
              top: -10px;
              width: 56px;
              height: 14px;
            }
          }
        }
      }
      .box_4 {
        box-shadow: 0px 0px 9px 0px rgba(157, 157, 157, 0.17);
        background-color: rgba(255, 255, 255, 1);
        border-radius: 20px 20px 0px 0px;
        width: 375px;
        margin-top: 12px;
        justify-content: flex-center;
        padding: 12px 36px 22px 36px;
        .image-text_3 {
          .label_2 {
            width: 24px;
            height: 24px;
            margin-right: 1px;
          }
          .text-group_5 {
            overflow-wrap: break-word;
            color: rgba(63, 127, 252, 1);
            font-size: 12px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 17px;
            margin: 5px 0 0 1px;
          }
        }
        .image-text_4 {
          margin-left: 62px;
          .label_3 {
            width: 24px;
            height: 24px;
          }
          .text-group_6 {
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 12px;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 17px;
            margin-top: 5px;
          }
        }
        .image-text_5 {
          margin-left: 46px;
          .label_4 {
            width: 24px;
            height: 24px;
            align-self: center;
          }
          .text-group_7 {
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 12px;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 17px;
            margin-top: 5px;
          }
        }
        .image-text_6 {
          margin-left: 50px;
          .label_5 {
            width: 24px;
            height: 24px;
          }
          .text-group_8 {
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 12px;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 17px;
            margin-top: 5px;
          }
        }
      }
    }
    .group_5 {
      position: absolute;
      left: 0;
      top: 307px;
      width: 375px;
      height: 232px;
      padding: 125px 9px 11px 10px;
      .box_5 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 2px;
        padding: 10px 9px 15px 9px;
        .section_1 {
          width: 338px;
          .group_6 {
            .text_15 {
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 15px;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 21px;
              margin-right: 36px;
            }
            .text-wrapper_4 {
              background-color: rgba(195, 222, 255, 1);
              border-radius: 1px;
              margin-top: 4px;
              padding: 0 4px 0 2px;
              .text_16 {
                overflow-wrap: break-word;
                color: rgba(49, 133, 188, 1);
                font-size: 10px;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 14px;
              }
            }
          }
          .text-wrapper_5 {
            background-color: rgba(63, 127, 252, 1);
            border-radius: 14px;
            margin: 4px 0 11px 0;
            padding: 3px 13px 4px 14px;
            .text_17 {
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 12px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 17px;
            }
          }
        }
        .section_2 {
          background-color: rgba(246, 246, 246, 1);
          border-radius: 1px;
          width: 338px;
          margin-top: 10px;
          padding: 4px 9px 4px 10px;
          .image-text_7 {
            width: 77px;
            .thumbnail_3 {
              width: 9px;
              height: 10px;
              margin: 2px 0 2px 0;
            }
            .text-group_9 {
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 10px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 14px;
            }
          }
          .image-text_8 {
            width: 71px;
            .thumbnail_4 {
              width: 7px;
              height: 10px;
              margin: 2px 0 2px 0;
            }
            .text-group_10 {
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 10px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 14px;
            }
          }
          .image-text_9 {
            width: 72px;
            .thumbnail_5 {
              width: 10px;
              height: 10px;
              margin: 2px 0 2px 0;
            }
            .text-group_11 {
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 10px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 14px;
            }
          }
        }
      }
      .box_6 {
        background-color: rgba(245, 245, 245, 1);
        height: 80px;
        width: 375px;
        position: absolute;
        left: 0;
        top: -35px;
        .box_7 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 10px;
          width: 356px;
          height: 80px;
          margin: -30px 10px 30px 9px;
          padding: 9px 20px 7px 20px;
          .image-text_10 {
            .label_6 {
              width: 36px;
              height: 36px;
              align-self: center;
            }
            .text-group_12 {
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 13px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 18px;
              margin-top: 10px;
            }
          }
          .image-text_11 {
            .label_7 {
              width: 36px;
              height: 36px;
              align-self: center;
            }
            .text-group_13 {
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 13px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 18px;
              margin-top: 10px;
            }
          }
          .image-text_12 {
            .label_8 {
              width: 36px;
              height: 36px;
              align-self: center;
            }
            .text-group_14 {
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 13px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 18px;
              margin-top: 10px;
            }
          }
          .image-text_13 {
            .label_9 {
              width: 36px;
              height: 36px;
              align-self: center;
            }
            .text-group_15 {
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 13px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 18px;
              margin-top: 10px;
            }
          }
        }
      }
      .box_8 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 2px;
        position: absolute;
        left: 10px;
        top: 25px;
        width: 356px;
        height: 90px;
        padding: 10px 9px 9px 9px;
        .group_7 {
          width: 338px;
          .group_8 {
            .text_18 {
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 15px;
              font-family: PingFangSC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 21px;
              margin-right: 34px;
            }
            .text-wrapper_6 {
              background-color: rgba(255, 232, 195, 1);
              border-radius: 1px;
              margin-top: 4px;
              padding: 0 2px 0 2px;
              .text_19 {
                overflow-wrap: break-word;
                color: rgba(188, 149, 49, 1);
                font-size: 10px;
                font-weight: normal;
                text-align: left;
                white-space: nowrap;
                line-height: 14px;
              }
            }
          }
          .text-wrapper_7 {
            background-color: rgba(63, 127, 252, 1);
            border-radius: 14px;
            margin: 4px 0 11px 0;
            padding: 3px 13px 4px 14px;
            .text_20 {
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 12px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 17px;
            }
          }
        }
        .group_9 {
          background-color: rgba(246, 246, 246, 1);
          border-radius: 1px;
          width: 338px;
          margin-top: 10px;
          padding: 4px 10px 4px 10px;
          .image-text_14 {
            width: 53px;
            .thumbnail_6 {
              width: 9px;
              height: 10px;
              margin: 2px 0 2px 0;
            }
            .text-group_16 {
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 10px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 14px;
            }
          }
          .image-text_15 {
            width: 52px;
            .thumbnail_7 {
              width: 9px;
              height: 10px;
              margin: 2px 0 2px 0;
            }
            .text-group_17 {
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 10px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 14px;
            }
          }
          .image-text_16 {
            width: 51px;
            .thumbnail_8 {
              width: 7px;
              height: 10px;
              margin: 2px 0 2px 0;
            }
            .text-group_18 {
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 10px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 14px;
            }
          }
          .image-text_17 {
            width: 52px;
            .thumbnail_9 {
              width: 10px;
              height: 10px;
              margin: 2px 0 2px 0;
            }
            .text-group_19 {
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 10px;
              font-weight: normal;
              text-align: left;
              white-space: nowrap;
              line-height: 14px;
            }
          }
        }
      }
    }
  }
  
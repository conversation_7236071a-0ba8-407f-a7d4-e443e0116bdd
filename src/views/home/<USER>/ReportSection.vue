<template>
  <div class="report-section" @click="openReport">
    <div class="report-icon">
      <van-icon name="warning-o" />
    </div>
    <div class="report-content">
      <div class="report-title">非法集资举报</div>
      <div class="report-subtitle">维护金融秩序，保护合法权益</div>
    </div>
    <van-icon name="arrow" class="arrow-icon" />
  </div>
</template>

<script>
export default {
  name: 'ReportSection',
  methods: {
    openReport() {
      this.$router.push('/report')
    }
  }
}
</script>

<style lang="scss" scoped>
.report-section {
  background-color: white;
  margin: 0 16px 16px;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  
  &:active {
    background-color: #f8f9fa;
  }
  
  .report-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    
    .van-icon {
      color: white;
      font-size: 20px;
    }
  }
  
  .report-content {
    flex: 1;
    
    .report-title {
      font-size: 15px;
      font-weight: 600;
      color: #323233;
      margin-bottom: 2px;
    }
    
    .report-subtitle {
      font-size: 12px;
      color: #646566;
    }
  }
  
  .arrow-icon {
    color: #c8c9cc;
    font-size: 16px;
  }
}
</style>

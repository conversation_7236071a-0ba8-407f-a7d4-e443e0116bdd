<template>
  <div class="hot-products-section">
    <div class="section-header">
      <div class="section-title">
        <van-icon name="fire-o" />
        <span>热门产品</span>
      </div>
      <div class="view-all" @click="viewAllProducts">
        查看全部
        <van-icon name="arrow" />
      </div>
    </div>

    <div class="products-list">
      <div v-for="product in products" :key="product.id" class="product-card">
        <div class="product-header">
          <h3 class="product-name">
            {{ product.cpmc }}
            <div class="desc">最低利率{{ product.cklvq || "---" }}%</div>
          </h3>
          <span class="bank">{{ product.sftycp }}</span>
          <!-- <span :src="product.sftycp" class="bank-logo" /> -->
        </div>

        <div class="product-amount">{{ product.dkedz }}万</div>
        <div class="product-description">最高可贷金额(元)</div>

        <van-button
          type="primary"
          size="small"
          round
          class="apply-button"
          @click.stop="applyProduct(product)"
        >
          我要申请
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "HotProductsSection",
  props: {
    products: {
      type: Array,
      required: true
    }
  },
  methods: {
    viewAllProducts() {
      this.$router.push({
        path: "/productList"
      });
    },
    viewProductDetail(product) {
      this.$router.push(`/product/${product.id}`);
    },
    applyProduct(product) {
      this.$router.push({
        path: "/productDetail",
        query: { product: JSON.stringify(product) }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.hot-products-section {
  margin: 0 16px 16px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #323233;

      .van-icon {
        margin-right: 6px;
        color: #ee0a24;
      }
    }

    .view-all {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #646566;
      cursor: pointer;

      .van-icon {
        margin-left: 4px;
        font-size: 12px;
      }
    }
  }

  .products-list {
    display: flex;
    flex-direction: column;
    padding-bottom: 68px;
    gap: 12px;

    .product-card {
      position: relative;
      background-color: white;
      border-radius: 12px;
      padding: 16px 10px;
      border: 2px solid #f5f5f5;
      cursor: pointer;
      transition: all 0.2s;
      background-color: #e6f5ff;
      // background: linear-gradient(134deg, #f3f8ff 0%, #dbf4ff 100%);
      background-image: url("~@/assets/images/home/<USER>");
      background-repeat: no-repeat;
      background-position: 110px 30px;
      background-size: contain;
      &:active {
        transform: scale(0.98);
      }

      .product-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .product-name {
          font-size: 18px;
          font-weight: 600;
          color: #323233;
          margin: 0;

          .desc {
            margin-top: 12px;
            width: 190px;
            // text-wrap: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            color: #999999;
            font-size: 14px;
          }
        }
        .bank {
          font-size: 14px;
          color: #646566;
          text-wrap: nowrap;
        }
        .bank-logo {
          width: 24px;
          height: 24px;
          border-radius: 4px;
        }
      }

      .product-amount {
        font-size: 26px;
        font-weight: 700;
        color: #323233;
        margin-bottom: 4px;
      }

      .product-description {
        font-size: 12px;
        color: #646566;
        margin-bottom: 12px;
      }

      .apply-button {
        position: absolute;
        height: 32px;
        padding: 0 16px;
        font-size: 13px;
        right: 20px;
        bottom: 20px;
        border: none;
        background: linear-gradient(270deg, #54aaff 0%, #3e89fd 100%);
        // box-shadow: 0px 2px 4px 0px rgba(77, 159, 255, 0.54);
      }
    }
  }
}
</style>

<template>
  <div class="financing-form">
    <!-- 自定义步骤指示器 -->
    <div class="header">
      <van-nav-bar
        title="融资需求发布"
        left-arrow
        @click-left="onClickLeft"
        class="custom-nav-bar"
      />
      <div class="custom-steps">
        <div class="step-item" :class="{ active: currentStep >= 1 }">
          <div class="step-circle">1</div>
          <div class="step-text">信息选择</div>
        </div>
        <div class="step-line" :class="{ active: currentStep >= 2 }"></div>
        <div class="step-item" :class="{ active: currentStep >= 2 }">
          <div class="step-circle">2</div>
          <div class="step-text">全部机构选择</div>
        </div>
        <div class="step-line" :class="{ active: currentStep >= 3 }"></div>
        <div class="step-item" :class="{ active: currentStep >= 3 }">
          <div class="step-circle">3</div>
          <div class="step-text">完成</div>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <van-cell-group>
        <!-- 办理类别 -->
        <van-field
          v-model="formData.category"
          label="办理类别"
          placeholder="请选择"
          readonly
          is-link
          @click="showCategoryPicker = true"
        />

        <!-- 联系人姓名 -->
        <van-field
          v-model="formData.contactName"
          label="联系人姓名"
          placeholder="请输入"
        />

        <!-- 手机号码 -->
        <van-field
          v-model="formData.phone"
          label="手机号码"
          placeholder="请输入"
          type="tel"
        />

        <!-- 身份证号码 -->
        <van-field
          v-model="formData.idCard"
          label="身份证号码"
          placeholder="请输入"
        />

        <!-- 所在区域 -->
        <van-field
          v-model="formData.region"
          label="所在区域"
          placeholder="请选择"
          readonly
          is-link
          @click="showRegionPicker = true"
        />

        <!-- 经营地址 -->
        <van-field
          v-model="formData.businessAddress"
          label="经营地址"
          placeholder="请输入"
        />

        <!-- 申请金额 -->
        <van-field
          v-model="formData.amount"
          label="申请金额"
          placeholder="请输入"
          type="number"
        >
          <template #right-icon>
            <span class="unit-text">万元</span>
          </template>
        </van-field>

        <!-- 申请期限 -->
        <van-field
          v-model="formData.period"
          label="申请期限"
          placeholder="请输入"
          type="number"
        >
          <template #right-icon>
            <span class="unit-text">个月</span>
          </template>
        </van-field>

        <!-- 融资用途 -->
        <van-field
          v-model="formData.purpose"
          label="融资用途"
          placeholder="请选择"
          readonly
          is-link
          @click="showPurposePicker = true"
        />

        <!-- 采购内容 -->
        <van-field
          v-model="formData.purchaseContent"
          label="采购内容"
          placeholder="请输入"
        />

        <!-- 采购金额 -->
        <van-field
          v-model="formData.purchaseAmount"
          label="采购金额"
          placeholder="请输入"
          type="number"
        >
          <template #right-icon>
            <span class="unit-text">万元</span>
          </template>
        </van-field>

        <!-- 担保方式 -->
        <van-field
          v-model="formData.guaranteeMethod"
          label="担保方式"
          placeholder="请选择"
          readonly
          is-link
          @click="showGuaranteePicker = true"
        />
      </van-cell-group>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-button">
      <van-button type="primary" block @click="nextStep">下一步</van-button>
    </div>

    <!-- 选择器弹窗 -->
    <van-popup v-model="showCategoryPicker" position="bottom">
      <van-picker
        :columns="categoryOptions"
        @confirm="onCategoryConfirm"
        @cancel="showCategoryPicker = false"
      />
    </van-popup>

    <van-popup v-model="showRegionPicker" position="bottom">
      <van-picker
        :columns="regionOptions"
        @confirm="onRegionConfirm"
        @cancel="showRegionPicker = false"
      />
    </van-popup>

    <van-popup v-model="showPurposePicker" position="bottom">
      <van-picker
        :columns="purposeOptions"
        @confirm="onPurposeConfirm"
        @cancel="showPurposePicker = false"
      />
    </van-popup>

    <van-popup v-model="showGuaranteePicker" position="bottom">
      <van-picker
        :columns="guaranteeOptions"
        @confirm="onGuaranteeConfirm"
        @cancel="showGuaranteePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: "FinancingForm",
  data() {
    return {
      currentStep: 1,
      formData: {
        category: "",
        contactName: "李丹",
        phone: "",
        idCard: "",
        region: "",
        businessAddress: "",
        amount: "",
        period: "",
        purpose: "",
        purchaseContent: "",
        purchaseAmount: "",
        guaranteeMethod: ""
      },
      showCategoryPicker: false,
      showRegionPicker: false,
      showPurposePicker: false,
      showGuaranteePicker: false,
      categoryOptions: ["个人贷款", "企业贷款", "抵押贷款", "信用贷款"],
      regionOptions: ["北京市", "上海市", "广州市", "深圳市", "杭州市"],
      purposeOptions: [
        "经营周转",
        "设备采购",
        "原材料采购",
        "扩大生产",
        "其他"
      ],
      guaranteeOptions: ["信用担保", "抵押担保", "质押担保", "保证担保"]
    };
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1);
    },
    nextStep() {
      // 表单验证逻辑
      if (this.validateForm()) {
        this.currentStep = 2;
        // 跳转到下一步或提交表单
        console.log("表单数据:", this.formData);
      }
    },
    validateForm() {
      // 简单的表单验证
      if (!this.formData.contactName) {
        this.$toast("请输入联系人姓名");
        return false;
      }
      if (!this.formData.phone) {
        this.$toast("请输入手机号码");
        return false;
      }
      return true;
    },
    onCategoryConfirm(value) {
      this.formData.category = value;
      this.showCategoryPicker = false;
    },
    onRegionConfirm(value) {
      this.formData.region = value;
      this.showRegionPicker = false;
    },
    onPurposeConfirm(value) {
      this.formData.purpose = value;
      this.showPurposePicker = false;
    },
    onGuaranteeConfirm(value) {
      this.formData.guaranteeMethod = value;
      this.showGuaranteePicker = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.financing-form {
  min-height: 100vh;
  background-color: #f7f8fa;
  .header {
    background: url("~@/assets/images/publish/header_bg.png") no-repeat;
    position: relative;
    height: 181px;
    background-size: contain;
    .custom-nav-bar {
      background: transparent;
      :deep(.van-nav-bar__title) {
        color: white;
        font-weight: 500;
      }

      :deep(.van-icon) {
        color: white;
      }
    }

    .custom-steps {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px 32px;

      .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 2;

        .step-circle {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.3);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 8px;
          border: 2px solid rgba(255, 255, 255, 0.5);
          transition: all 0.3s ease;
        }

        .step-text {
          color: rgba(255, 255, 255, 0.8);
          font-size: 12px;
          text-align: center;
          transition: all 0.3s ease;
        }

        &.active {
          .step-circle {
            background-color: white;
            color: #29b6f6;
            border-color: white;
          }

          .step-text {
            color: white;
            font-weight: 500;
          }
        }
      }

      .step-line {
        flex: 1;
        height: 2px;
        background-color: rgba(255, 255, 255, 0.3);
        margin: 0 10px;
        margin-bottom: 24px;
        transition: all 0.3s ease;

        &.active {
          background-color: white;
        }
      }
    }
  }

  .form-content {
    padding: 16px;

    :deep(.van-cell-group) {
      border-radius: 8px;
      overflow: hidden;
    }

    :deep(.van-field) {
      padding: 16px;

      .van-field__label {
        color: #323233;
        font-weight: 500;
        width: 80px;
      }

      .van-field__control {
        color: #646566;
      }

      .van-field__control::placeholder {
        color: #c8c9cc;
      }
    }

    .unit-text {
      color: #969799;
      font-size: 14px;
      margin-right: 8px;
    }
  }

  .bottom-button {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background-color: white;
    border-top: 1px solid #ebedf0;

    :deep(.van-button--primary) {
      background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
      border: none;
      height: 48px;
      border-radius: 24px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  // 选择器样式
  :deep(.van-picker) {
    .van-picker__toolbar {
      padding: 16px;
    }

    .van-picker__confirm {
      color: #29b6f6;
    }
  }
}

// 响应式适配
// @media (max-width: 375px) {
//   .financing-form {
//     .custom-steps {
//       padding: 16px 12px;

//       .step-item {
//         .step-circle {
//           width: 28px;
//           height: 28px;
//           font-size: 12px;
//         }

//         .step-text {
//           font-size: 11px;
//         }
//       }

//       .step-line {
//         margin: 0 8px;
//       }
//     }

//     .form-content {
//       padding: 12px;
//     }
//   }
// }
</style>

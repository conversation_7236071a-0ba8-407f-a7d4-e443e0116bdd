import { ref } from "vue";
import { useUserStore } from "@/store/modules/user";
import { devAsdUserinfo } from "@/constants";
import { useRouter } from "@/composition-helpers";
const loading = ref(false);
export function useAsdLogin() {
  const userStore = useUserStore();
  const router = useRouter();
  function oncloseWindow() {
    lightAppJssdk.navigation.close({
      success: () => {},
      fail: () => {}
    });
  }

  function onLoginApp() {
    lightAppJssdk.user.loginapp({
      success: data => {
        if (data == "未登录") {
          oncloseWindow();
        } else {
          loginAction();
        }
      },
      fail: () => {
        oncloseWindow();
      }
    });
  }

  /** 爱山东获取用户信息 */
  async function loginAction() {
    if (process.env.NODE_ENV === "development") {
      await toLogin(devAsdUserinfo);
      return;
    }
    const appId = qdeiccAsdConfig.appId;
    console.log("asd login loginAction appId=" + appId);
    lightAppJssdk.user.getUserInfoWithEncryptedParamByAppId({
      appId: appId,
      success: async data => {
        console.log("callback data:", data);
        if (data == "未登录") {
          onLoginApp();
        } else {
          if (typeof data == "string") data = JSON.parse(data);
          let encryptResult = data.data;
          loading.value = true;
          await toLogin(encryptResult).catch(e => {
            loading.value = false;
            console.log("登录失败", e);
          });
          loading.value = false;
          tryGoPage();
        }
      },
      fail: data => {}
    });
  }
  /** 调取后端登录 */
  async function toLogin(res) {
    console.log("toLogin");
    if (typeof res === "string") res = JSON.parse(res);
    /** 给后端发送前根据需要 格式化参数 */
    await userStore.login(handleParams(res)).catch(() => {
      Notify({ type: "danger", message: "登录异常，请刷新重新登录" });
    });
    Notify({ type: "success", message: "登录成功" });
  }
  function handleParams(res) {
    console.log(res);
    const params = {};
    let needParamsKey = [];
    if (res.cornumber) {
      res.userType = 2;
      /** 法人 */
      needParamsKey = [
        "name",
        "uuid",
        "mobile",
        "userType",
        "corusername",
        "cornumber",
        "corname",
        "cornumber",
        "cortype",
        "corrole",
        "corusercardid",
        "corusermobile",
        "isauthuser",
        "paperstype",
        "loginname",
        "newauthlevel",
        "authlevel"
      ];
    } else {
      res.userType = 1;
      needParamsKey = [
        "name",
        "uuid",
        "mobile",
        "userType",
        "isauthuser",
        "paperstype",
        "loginname",
        "newauthlevel",
        "authlevel"
      ];
    }
    needParamsKey.forEach(key => {
      params[key] = res[key];
    });
    params["idCard"] =
      params.userType === 2 ? res["cardid"] : res["papersnumber"];

    sessionStorage.setItem("loginParams", JSON.stringify(params));
    return params;
  }
  async function isAlreadyLogin() {
    if (!userStore.isLogin) {
      return false;
    } else {
      return true;
    }
  }

  function tryGoPage() {
    router.push({ path: "/" });
    // console.log("asd login tryGoPage", store.getters.asyncRoutesAdded);
    // if (
    //   redirectUrl.value &&
    //   (redirectUrl.value.toLowerCase().startsWith("http://") ||
    //     redirectUrl.value.toLowerCase().startsWith("https://"))
    // ) {
    //   window.location = redirectUrl.value;
    //   return;
    // }
    // if (!store.getters.asyncRoutesAdded) {
    //   store
    //     .dispatch("router/GenerateRoutes")
    //     .then(() => {
    //       console.log("asd login GenerateRoutes", store.getters.addRouters);
    //       router.addRoutes(store.getters.addRouters); // 动态添加可访问路由表
    //       goPage();
    //     })
    //     .catch(e => {
    //       console.log("asd login GenerateRoutes error");
    //     });
    // } else {
    //   goPage();
    // }
  }

  return {
    isAlreadyLogin,

    onLoginApp,
    oncloseWindow,
    loginAction,
    getLoginInfo,
    handleParams,
    toLogin,
    onLoginApp
  };
}

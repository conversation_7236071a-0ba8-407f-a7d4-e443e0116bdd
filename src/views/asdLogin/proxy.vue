<template>
  <div v-loading="loading" style="height:100vw;width:10vh" />
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { useUserStore } from "@/store/modules/user";
import { useRouter, useRoute, store } from "@/composition-helpers";
import { devAsdUserinfo } from "@/constants";
const userStore = useUserStore();
const router = useRouter();
const route = useRoute();

// 2. data
const loading = ref(true);
const redirectUrl = ref(null);

function oncloseWindow() {
  lightAppJssdk.navigation.close({
    success: data => {},
    fail: data => {}
  });
}

function onLoginApp() {
  lightAppJssdk.user.loginapp({
    success: data => {
      if (data == "未登录") {
        oncloseWindow();
      } else {
        loginAction();
      }
    },
    fail: data => {
      oncloseWindow();
    }
  });
}
function aes_Decrypt(word, key) {
  key = CryptoJS.enc.Utf8.parse(key);
  const srcs = CryptoJS.enc.Hex.parse(word);
  const str = CryptoJS.enc.Base64.stringify(srcs);
  const decrypt = CryptoJS.AES.decrypt(str, key, {
    mode: CryptoJS.mode.ECB,
    spadding: CryptoJS.pad.Pkcs7
  });
  return decrypt.toString(CryptoJS.enc.Utf8);
}

function sm2_Decrypt(word, key) {
  if (word && key) return window.SM.decrypt(word, key);
}
async function loginAction() {
  if (process.env.NODE_ENV === "development") {
    await toLogin(devAsdUserinfo);
    return;
  }
  const appId = qdeiccAsdConfig.appId;
  console.log("asd login loginAction appId=" + appId);
  lightAppJssdk.user.getUserInfoWithEncryptedParamByAppId({
    appId: appId,
    success: async data => {
      console.log("callback data:", data);
      if (data == "未登录") {
        onLoginApp();
      } else {
        // if (typeof data == "string") data = JSON.parse(data);
        // let encryptResult = data.data;
        // loading.value = true;
        // await toLogin(encryptResult).catch(e => {
        //   loading.value = false;
        //   console.log("登录失败", e);
        // });
        loading.value = true;
        if (typeof data === "string") {
          data = JSON.parse(data);
        }
        console.log("返回的 data", data, data.data);
        const sm2_encrypt_result = data.data;
        let sm2_decrypt_result = sm2_Decrypt(
          sm2_encrypt_result,
          "008261b96437c00ee9eae56e4604dbe578b12acc45255f99c35051dbf781e54c18"
        );
        console.log("sm2_decrypt_result", sm2_decrypt_result);
        if (typeof sm2_decrypt_result === "string") {
          sm2_decrypt_result = JSON.parse(sm2_decrypt_result);
        }
        console.log("sm2_decrypt_result obj", sm2_decrypt_result);
        // console.log(sm2_decrypt_result);
        const aes_encrypt_result = sm2_decrypt_result.data;
        const aes_decrypt_result = aes_Decrypt(
          aes_encrypt_result,
          "aKd20dbGdFvmuwrt"
        );
        console.log("=====获取用户信息成功2======----", aes_decrypt_result);

        await toLogin(aes_decrypt_result).catch(e => {
          loading.value = false;
          console.log("登录失败", e);
        });
        loading.value = false;
        tryGoPage();
      }
    },
    fail: data => {}
  });
}
async function toLogin(res) {
  console.log("toLogin");
  if (typeof res === "string") res = JSON.parse(res);
  /** 给后端发送前根据需要 格式化参数 */
  const params = handleParams(res);
  let info;
  try {
    // info = await login(params);
    console.log("params", params);
    loading.value = true;
    await userStore.login(params).catch(() => {
      Notify({ type: "danger", message: "登录异常，请刷新重新登录" });
      // throw new Error("登录异常，请刷新重新登录");
      loading.value = false;
    });
    loading.value = false;
    router.push({ path: "/" });
  } catch (error) {
    info = null;
    // Taro.hideLoading();
    // Taro.showToast({
    //   title: "登录异常，请刷新重新登录",
    //   icon: "none",
    //   duration: 1000,
    // });
  }
}
function handleParams(res) {
  console.log(res);
  const params = {};
  let needParamsKey = [];
  if (res.cornumber) {
    res.userType = 2;
    /** 法人 */
    needParamsKey = [
      "name",
      "uuid",
      "mobile",
      "userType",
      "corusername",
      "cornumber",
      "corname",
      "cornumber",
      "cortype",
      "corrole",
      "corusercardid",
      "corusermobile",
      "isauthuser",
      "paperstype",
      "loginname",
      "newauthlevel",
      "authlevel"
    ];
  } else {
    res.userType = 1;
    needParamsKey = [
      "name",
      "uuid",
      "mobile",
      "userType",
      "isauthuser",
      "paperstype",
      "loginname",
      "newauthlevel",
      "authlevel"
    ];
  }
  needParamsKey.forEach(key => {
    params[key] = res[key];
  });
  params["idCard"] =
    params.userType === 2 ? res["cardid"] : res["papersnumber"];

  sessionStorage.setItem("loginParams", JSON.stringify(params));
  return params;
}
async function isAlreadyLogin() {
  if (!userStore.isLogin) {
    return false;
  }
  // const loginInfo = await getLoginInfo(rawResponse => {
  //   if (rawResponse.code !== retCode.RET_OK) {
  //     return {};
  //   } else {
  //     return rawResponse.data;
  //   }
  // });
  // if (loginInfo?.id) {
  //   return true;
  // }
  return false;
}

function tryGoPage() {
  // console.log("asd login tryGoPage", store.getters.asyncRoutesAdded);
  if (
    redirectUrl.value &&
    (redirectUrl.value.toLowerCase().startsWith("http://") ||
      redirectUrl.value.toLowerCase().startsWith("https://"))
  ) {
    window.location = redirectUrl.value;
    return;
  }
  // if (!store.getters.asyncRoutesAdded) {
  //   store
  //     .dispatch("router/GenerateRoutes")
  //     .then(() => {
  //       // console.log("asd login GenerateRoutes", store.getters.addRouters);
  //       router.addRoutes(store.getters.addRouters); // 动态添加可访问路由表
  //       goPage();
  //     })
  //     .catch(e => {
  //       console.log("asd login GenerateRoutes error");
  //     });
  // } else {
  //   goPage();
  // }
}

function goPage() {
  console.log("asd login goPage", redirectUrl.value);
  if (redirectUrl.value) {
    const query = { ...route.query };
    delete query["redirectUrl"];
    nextTick(() => {
      router.push({ name: redirectUrl.value, query: query });
    });
  } else {
    nextTick(() => {
      router.push({ name: "home" });
    });
  }
}

// 5. mounted
onMounted(async () => {
  redirectUrl.value = route.query.redirectUrl;
  const alreadyLogin = await isAlreadyLogin();
  console.log("asd login", redirectUrl.value, alreadyLogin);
  if (alreadyLogin) {
    tryGoPage();
  } else {
    loginAction();
  }
});
</script>

<template>
  <div class="institution-list">
    <!-- 头部导航 -->
    <van-nav-bar
      title="机构列表"
      left-arrow
      @click-left="onClickLeft"
      class="custom-nav-bar"
    >
      <template #right>
        <van-icon name="scan" class="scan-icon" @click="onScan" />
      </template>
    </van-nav-bar>

    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchValue"
        placeholder="请输入关键搜索词"
        shape="round"
        background="transparent"
        @search="onSearch"
      />
    </div>

    <!-- 分类标签 -->
    <div class="category-tabs">
      <van-tabs v-model="activeCategory" @click="onCategoryChange">
        <van-tab title="银行" name="bank" />
        <van-tab title="担保" name="guarantee" />
        <van-tab title="保险" name="insurance" />
      </van-tabs>
    </div>

    <!-- 机构列表 -->
    <div class="institution-content">
      <institution-card
        v-for="(institution, index) in filteredInstitutions"
        :key="index"
        :institution="institution"
        @click="onInstitutionClick"
      />

      <!-- 空状态 -->
      <van-empty
        v-if="filteredInstitutions.length === 0"
        description="暂无相关机构"
        image="search"
      />
    </div>
  </div>
</template>

<script>
import InstitutionCard from "@/components/InstitutionCard";

export default {
  name: "InstitutionList",
  components: {
    InstitutionCard
  },
  data() {
    return {
      searchValue: "",
      activeCategory: "bank",
      institutionList: [
        {
          id: 1,
          name: "中国工商银行股份有限公司青岛分行",
          type: "bank",
          logo: "/placeholder.svg?height=32&width=32&text=工行",
          productCount: 8,
          hasProducts: true,
          description: "国有大型商业银行",
          address: "青岛市市南区香港中路59号"
        },
        {
          id: 2,
          name: "青岛农商银行",
          type: "bank",
          logo: "/placeholder.svg?height=32&width=32&text=农商",
          productCount: 3,
          hasProducts: true,
          description: "地方性股份制商业银行",
          address: "青岛市市北区延吉路112号"
        },
        {
          id: 3,
          name: "中国银行股份有限公司青岛分行",
          type: "bank",
          logo: "/placeholder.svg?height=32&width=32&text=中行",
          productCount: 6,
          hasProducts: true,
          description: "国有大型商业银行",
          address: "青岛市市南区中山路10号"
        },
        {
          id: 4,
          name: "中国建设银行股份有限公司青岛分行",
          type: "bank",
          logo: "/placeholder.svg?height=32&width=32&text=建行",
          productCount: 6,
          hasProducts: true,
          description: "国有大型商业银行",
          address: "青岛市市南区东海西路12号"
        },
        {
          id: 5,
          name: "北京银行股份有限公司青岛分行",
          type: "bank",
          logo: "/placeholder.svg?height=32&width=32&text=京行",
          productCount: 4,
          hasProducts: true,
          description: "股份制商业银行",
          address: "青岛市市南区山东路6号"
        },
        {
          id: 6,
          name: "威海市商业银行股份有限公司青岛分行",
          type: "bank",
          logo: "/placeholder.svg?height=32&width=32&text=威商",
          productCount: 4,
          hasProducts: true,
          description: "城市商业银行",
          address: "青岛市市北区辽宁路228号"
        },
        {
          id: 7,
          name: "青岛融资担保有限公司",
          type: "guarantee",
          logo: "/placeholder.svg?height=32&width=32&text=担保",
          productCount: 5,
          hasProducts: true,
          description: "专业融资担保机构",
          address: "青岛市市南区香港中路40号"
        },
        {
          id: 8,
          name: "山东省融资担保集团",
          type: "guarantee",
          logo: "/placeholder.svg?height=32&width=32&text=鲁担",
          productCount: 7,
          hasProducts: true,
          description: "省级融资担保机构",
          address: "青岛市市南区东海西路39号"
        },
        {
          id: 9,
          name: "中国人寿保险股份有限公司青岛分公司",
          type: "insurance",
          logo: "/placeholder.svg?height=32&width=32&text=国寿",
          productCount: 3,
          hasProducts: true,
          description: "大型保险公司",
          address: "青岛市市南区香港中路76号"
        },
        {
          id: 10,
          name: "中国平安保险股份有限公司青岛分公司",
          type: "insurance",
          logo: "/placeholder.svg?height=32&width=32&text=平安",
          productCount: 4,
          hasProducts: true,
          description: "综合性保险集团",
          address: "青岛市市南区东海西路15号"
        }
      ]
    };
  },
  computed: {
    filteredInstitutions() {
      let filtered = this.institutionList.filter(
        item => item.type === this.activeCategory
      );

      if (this.searchValue) {
        filtered = filtered.filter(
          item =>
            item.name.includes(this.searchValue) ||
            item.description.includes(this.searchValue)
        );
      }

      return filtered;
    }
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1);
    },
    onScan() {
      console.log("扫码功能");
      // 实现扫码功能
    },
    onSearch(value) {
      console.log("搜索:", value);
    },
    onCategoryChange(name, title) {
      console.log("切换分类:", name, title);
    },
    onInstitutionClick(institution) {
      console.log("点击机构:", institution);
      this.$router.push({
        name: "InstitutionDetail",
        params: { id: institution.id }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.institution-list {
  min-height: 100vh;
  background-color: #f7f8fa;

  .custom-nav-bar {
    background-color: white;

    :deep(.van-nav-bar__title) {
      color: #323233;
      font-weight: 500;
    }

    .scan-icon {
      color: #969799;
      font-size: 18px;
    }
  }

  .search-section {
    padding: 12px 16px;
    background-color: white;

    :deep(.van-search) {
      padding: 0;

      .van-search__content {
        background-color: #f7f8fa;
        border-radius: 20px;
      }

      .van-field__control {
        font-size: 14px;
      }
    }
  }

  .category-tabs {
    background-color: white;
    border-bottom: 1px solid #ebedf0;

    :deep(.van-tabs) {
      .van-tabs__wrap {
        padding: 0 16px;
      }

      .van-tab {
        font-size: 15px;
        color: #646566;

        &.van-tab--active {
          color: #1989fa;
          font-weight: 500;
        }
      }

      .van-tabs__line {
        background-color: #1989fa;
        width: 20px;
        border-radius: 2px;
      }
    }
  }

  .institution-content {
    padding: 8px 16px 16px;

    :deep(.van-empty) {
      padding: 60px 0;
    }
  }
}
</style>

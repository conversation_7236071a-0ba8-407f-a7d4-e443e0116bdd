import router from "./router";

import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { getRandom } from "@/utils/index";

import { useUserStore } from "@/store/modules/user";

NProgress.configure({ showSpinner: false });
// 未登录可访问的页面
const whiteList = [
  // "/login-asd",
  // "/home",
  // "/publish",
  // "/productList",
  // "/orgList"
];

router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore();
  const isLogin = userStore.isLogin;
  // NProgress.start();
  document.title = "烟台市信用服务平台";
  if (
    to.path === "/login-portal" ||
    to.path === "/login-sd" ||
    to.path === "/login-asd"
  ) {
    // 打开登录中转页面时，直接跳转中转页面，后续判断逻辑在中转页面处理
    next();
    // NProgress.done();
    return;
  }
  if (
    window.qdeiccIsAsd &&
    !isLogin &&
    (to.path === "/" || to.path === "/home")
  ) {
    // 爱山东模式下，首页进入先进登录页
    next({
      path: "/login-asd",
      query: { redirectUrl: "/" },
      replace: true
    });
    // NProgress.done();
    return;
  }

  console.log("permission", to.path, isLogin);
  if (isLogin) {
    next();
  } else {
    //未登录
    if (whiteList.indexOf(to.path) !== -1) {
      // 未登录可访问的页面，直接访问
      next();
      // NProgress.done();
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      // 跳转到登录页
      next("/login-asd");
      // NProgress.done();
    }
  }
});

function getPath(to) {
  if (to.fullPath.indexOf("?") > -1) {
    const ind = to.fullPath.indexOf("?m=");
    if (ind > -1) {
      return to.path + "?m=" + getRandom();
    }
    const index = to.fullPath.indexOf("&m=");
    if (index > -1) {
      return to.fullPath.substring(0, index) + "&m=" + getRandom();
    } else {
      return to.fullPath + "&m=" + getRandom();
    }
  } else {
    return to.path + "?m=" + getRandom();
  }
}
function getRoute() {
  // TODO 根据用户身份，企业默认打开home，政府端默认打开staging
  return "/home?m=" + getRandom();
}

import Vue from "vue";
import Router from "vue-router";
Vue.use(Router);
export const constantRoutes = [
  {
    path: "/",
    name: "home",

    meta: { title: "首页" },
    component: () => import("@/views/home/<USER>")
  },
  {
    path: "/login-asd",
    component: () => import("@/views/asdLogin/proxy"),
    hidden: true
  },
  {
    path: "/publish",
    component: () => import("@/views/publish/index"),
    hidden: true
  },
  {
    path: "/productList",
    component: () => import("@/views/productList/index"),
    hidden: true
  },
  {
    path: "/orgList",
    component: () => import("@/views/orgList/index"),
    hidden: true
  },
  {
    path: "/policyList",
    component: () => import("@/views/policyList/index"),
    hidden: true
  },
  {
    path: "/policyDetail",
    component: () => import("@/views/policyDetail/index"),
    hidden: true
  },
  {
    path: "/orgDetail",
    component: () => import("@/views/orgDetail/index"),
    hidden: true
  },
  {
    path: "/productDetail",
    component: () => import("@/views/productDetail/index"),
    hidden: true
  },
  {
    path: "/mine",
    component: () => import("@/views/mine/index"),
    hidden: true
  },
  {
    path: "/zone",
    component: () => import("@/views/zone/index"),
    hidden: true
  },
  {
    path: "/companyList",
    component: () => import("@/views/companyList/index"),
    hidden: true
  },
  {
    path: "/need",
    component: () => import("@/views/need/index"),
    hidden: true
  },
  {
    path: "/needDetail",
    component: () => import("@/views/needDetail/index"),
    hidden: true
  },
  {
    path: "/activity",
    component: () => import("@/views/activity/index"),
    hidden: true
  }
];
export const serverRoutes = [];

export const asyncRoutes = [
  {
    path: "/error",
    hidden: true,
    component: {
      render(c) {
        return c("router-view");
      }
    },
    meta: { title: "系统错误" },
    children: [
      {
        path: "/notFound",
        name: "notFound",
        meta: { title: "未知页面" },
        component: () => import("@/views/404")
      },
      {
        path: "/noPermission",
        name: "noPermission",
        meta: { title: "无权访问" },
        component: () => import("@/views/403")
      }
    ]
  },
  // 404 page must be placed at the end !!!
  { path: "*", redirect: "/notFound", hidden: true }
];
const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;

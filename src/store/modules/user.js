import { defineStore } from "pinia";
import { login } from "@/api/login";
import { sStorage } from "@/utils/storange";
export const useUserStore = defineStore("user", {
  state: () => ({
    token: sStorage.get("token") || "",
    userInfo: sStorage.get("userInfo") || {},
    isLogin: sStorage.get("isLogin") || false
  }),
  actions: {
    async login(params) {
      const res = await login(params);
      this.isLogin = true;
      this.token = res.token;
      this.userInfo = res;
      sStorage.set("token", res.token);
      sStorage.set("userInfo", res);
      sStorage.set("isLogin", true);

      return res;
    }
  }
});

// import { getErrorMessageFilter } from 'baseModule@/config/baseConfig'
// import { Message } from 'element-ui'
// import Taro from '@tarojs/taro'
import { useUserStore } from "@/store/modules/user";
import retCode from "./retCode";
import qs from "qs";
import CryptoJS from "crypto-js";

const aesKey = "SQxlea4xGJhqmppenxhw44OxEYy#bmxr";
var message = "";
const service = async config => {
  const userStore = useUserStore();
  if (!config.params) {
    config.params = {};
  }
  if (!config.headers) {
    config.headers = {};
  }
  if (config.method === "get") {
    delete config.headers["Content-Type"];
  }
  // 爱山东的api请求，不携带域名，只需要path部分
  if (
    config.url.startsWith("http://") ||
    config.url.startsWith("https://") ||
    config.url.startsWith("//")
  ) {
    config.url =
      "/" +
      config.url
        .split("//")[1]
        .split("/")
        .splice(1)
        .join("/");
  }
  // 处理 url ./api/xxx/bbb ../api/xxx/bbb
  if (config.url.startsWith(".")) {
    config.url = config.url.split(".").join("");
  }

  config.params["_"] = Math.random();
  // console.log('request init ', config.url, config.data, JSON.stringify(config))
  // 加密后数据都是字符串，需要手动指定解密后的格式
  // asd网关封包前也需要手动指定类型
  if (config.data) {
    if (!config.headers["Content-Type"]) {
      if (typeof config.data === "string") {
        config.headers["Content-Type"] = "application/x-www-form-urlencoded";
      } else if (config.data instanceof FormData) {
        config.headers["Content-Type"] = "multipart/form-data";
      } else if (typeof config.data === "object") {
        config.headers["Content-Type"] = "application/json;charset=UTF-8";
      }
    }
    // 处理body数据加密
    if (config.data instanceof FormData) {
      const strData = {};
      const fileList = [];
      config.data.forEach((value, key) => {
        if (value instanceof File) {
          fileList.push({
            fileName: key,
            file: value
          });
        } else {
          strData[key] = value;
        }
      });
      config.data = strData;
      config.fileList = fileList;
      console.log(
        "process",
        process.env.VUE_APP_HTTP_CYPTO,
        typeof process.env.VUE_APP_HTTP_CYPTO
      );
    } else if (process.env.VUE_APP_HTTP_CYPTO === "true") {
      // config.headers['crypto'] = '1'
      config.headers["rencai"] = "1";
      const isJsonData = typeof config.data === "object";
      const keyHex = CryptoJS.enc.Utf8.parse(aesKey);
      const encrypted = CryptoJS.AES.encrypt(
        isJsonData ? JSON.stringify(config.data) : config.data,
        keyHex,
        {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7
        }
      );
      const enc = encrypted.ciphertext.toString(CryptoJS.enc.Base64);
      config.rawData = config.data;
      // 爱山东的bizContent必须是json对象
      config.data = { data: enc };
    } else {
      console.log(
        "process",
        process.env.VUE_APP_HTTP_CYPTO,
        typeof process.env.VUE_APP_HTTP_CYPTO
      );
      // 爱山东的bizContent必须是json对象
      console.log(
        "不加密,config.data",
        config.data,
        typeof config.data,
        JSON.stringify(config.data),
        typeof JSON.stringify(config.data)
      );
      config.data = { data: config.data };
    }
  }

  // 爱山东的一些代理 无需修改
  const asdProxyToken = {};
  asdProxyToken.contentType = config.headers["Content-Type"];
  asdProxyToken.method = config.method;
  asdProxyToken.url = config.url;
  asdProxyToken.queryString = qs.stringify(config.params, {
    arrayFormat: "repeat"
  });
  // console.log('request get sessionId :',config.url, localStorage.getItem('sessionId'))
  if (localStorage.getItem("sessionId")) {
    asdProxyToken.sessionId = localStorage.getItem("sessionId");
  }
  const keyHex = CryptoJS.enc.Utf8.parse(aesKey);

  const encryptedToken = CryptoJS.AES.encrypt(
    JSON.stringify(asdProxyToken),
    keyHex,
    {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    }
  );

  // 自己的头参数 根据子的项目修改
  config.headers["Asd-Proxy-Token"] = encryptedToken.ciphertext.toString(
    CryptoJS.enc.Base64
  );
  config.headers["crypto"] = "0";
  config.headers["ext1"] = "";
  // config.headers['rencai'] = '1'
  // Authorization: config.headers['Authorization']
  config.headers["raw-request"] = JSON.stringify(asdProxyToken);
  const token = userStore.token;
  token && (config.headers["Authorization"] = token);
  if (config.headers["Content-Type"]) {
    delete config.headers["Content-Type"];
  }

  console.log("config.fileList,这是上传文件", config.fileList);
  // debugger
  const proxyInterface = config.fileList
    ? process.env.VUE_APP_ASD_PROXY_FILE
    : process.env.VUE_APP_ASD_PROXY_DATA;
  console.log(
    "appid",
    qdeiccAsdConfig.appId,
    "proxyInterface:接口id",
    proxyInterface,
    typeof proxyInterface
  );
  const value = await vaildInterfacefn(
    qdeiccAsdConfig.appId,
    proxyInterface,
    config.data ? JSON.stringify(config.data) : null,
    "2",
    { header: config.headers, fileList: config.fileList ? config.fileList : [] }
  );

  console.log(config.url);
  // const b = performance.now()
  console.log("这是解密前的数据", config.url, value.length, value);
  // let resData = window.SM.doDecrypt(value ? value.substring(2) : '', '00f6ca310e4548b2767a5cfb8f75824e3759b3ba3dd3fab21960c83c7dbb6b0201')
  let resData = sm2.doDecrypt(
    value ? value.substring(2) : "",
    qdeiccAsdConfig.privateKey
  );
  // console.log(config.url + '解密耗时:' + (performance.now() - b) + '毫秒')
  // const startTemp = resData.substr(0, 10)
  // const endTemp = resData.substr(-10)
  // console.log('request decSm',config.url,resData?.length,'start:'+startTemp,'end:'+endTemp,resData)
  // 爱山东不会传递服务器返回的header，因此只能通过"开头 来判断数据是加密的
  if (resData.startsWith('"')) {
    try {
      const keyHex = CryptoJS.enc.Utf8.parse(aesKey);
      resData = resData.substr(1);
      resData = resData.substr(0, resData.length - 1);
      const decrypted = CryptoJS.AES.decrypt(
        {
          ciphertext: CryptoJS.enc.Base64.parse(resData)
        },
        keyHex,
        {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7
        }
      );
      resData = decrypted.toString(CryptoJS.enc.Utf8);
      // const startTemp2 = resData.substr(0, 10)
      // const endTemp2 = resData.substr(-10)
      // console.log('request decSelf ',config.url,resData?.length,'start:'+startTemp2,'end:'+endTemp2,resData)
    } catch (e) {}
  }
  console.log("这是解密后的数据", resData);
  resData = JSON.parse(resData);
  const sessionId = resData.sessionId;
  // const obj = JSON.parse(resData.data)
  // resData = obj
  localStorage.setItem("sessionId", sessionId);

  const res = resData;
  if (config.rawResponseHandler) {
    return config.rawResponseHandler(res);
  }
  if (res.code !== retCode.RET_OK) {
    if (res.message !== message && !config.hideMessage) {
      // Taro.showToast({
      //   title: res.message || 'Error',
      //   icon: 'none',
      //   duration: 2500
      // })
      message = res.message;
    }

    if (res.code === retCode.RET_NOT_LOGIN) {
      // to re-login
      store.commit("user/RESET_STATE");
      store.dispatch("router/resetRouter");
      location.reload();
      return;
    }
    if (res.code === retCode.TO_LOGIN) {
      store.dispatch("user/logout");
      return Promise.reject(res.message);
    }

    return Promise.reject(res);
  } else {
    console.log("这是最终要渲染的数据", res.data);
    return res.data;
  }
};

export default service;

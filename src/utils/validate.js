/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

const phoneReg = /^[1][0,1,2,3,4,5,6,7,8,9][0-9]{9}$/
const emailReg = /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/

/**
 * 是否是手机号格式
 * @param {number} str
 * @returns {Boolean}
 */
export function isPhoneNo(phoneNo) {
  return phoneReg.test(phoneNo)
}

/**
 * 是否是邮箱格式
 * @param {String} str
 * @returns {Boolean}
 */
export function isEmail(email) {
  return emailReg.test(email)
}

const telephoneReg = /^\d{3}-\d{7,8}|\d{4}-\d{7,8}$/

/**
 * 是否是固定电话格式
 * @param {String} no
 * @returns {Boolean}
 */
export function isTelephone(no) {
  return telephoneReg.test(no)
}

export function validateIdCard(idCard) {
  // 15位和18位身份证号码的正则表达式
  var regIdCard = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/
  // 如果通过该验证，说明身份证格式正确，但准确性还需计算
  if (regIdCard.test(idCard)) {
    if (idCard.length === 18) {
      var idCardWi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2] // 将前17位加权因子保存在数组里
      var idCardY = [1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2] // 这是除以11后，可能产生的11位余数、验证码，也保存成数组
      var idCardWiSum = 0 // 用来保存前17位各自乖以加权因子后的总和
      for (var i = 0; i < 17; i++) {
        idCardWiSum += idCard.substring(i, i + 1) * idCardWi[i]
      }
      var idCardMod = idCardWiSum % 11// 计算出校验码所在数组的位置
      var idCardLast = idCard.substring(17)// 得到最后一位身份证号码
      // 如果等于2，则说明校验码是10，身份证号码最后一位应该是X
      if (idCardMod === 2) {
        if (idCardLast === 'X' || idCardLast === 'x') {
          return true
          // alert("恭喜通过验证啦！");
        } else {
          return false
          // alert("身份证号码错误！");
        }
      } else {
        // 用计算出的验证码与最后一位身份证号码匹配，如果一致，说明通过，否则是无效的身份证号码
        if (idCardLast === idCardY[idCardMod] + '') {
          // alert("恭喜通过验证啦！");
          return true
        } else {
          return false
          // alert("身份证号码错误！");
        }
      }
    }
  } else {
    // alert("身份证格式不正确!");
    return false
  }
}

// export function validateIdCard(idCard) {
//   var regIdCard = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/
//   if (regIdCard.test(idCard)) {
//     return true
//   } else {
//     return false
//   }
// }

/**
 * 是否是统一社会信用代码
 * @param {String} socialCode
 * @returns {Boolean}
 */
export function isSocialCode(socialCode) {
  const reg = /[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}/
  return reg.test(socialCode)
}

/**
 * 是否大于零且保留两位小数
 * @param {String} value
 * @returns {Boolean}
 */
export function isPrice(value) {
  const reg = /^0\.([1-9]|\d[1-9])$|^[1-9]\d{0,8}\.\d{0,2}$|^[1-9]\d{0,8}$/
  return reg.test(value)
}

export function idNoFormatCheck(value) {
  let dtmBirth
  const num = value.toUpperCase()
  // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X。
  const reg = /^(\d{18,18}|\d{15,15}|\d{17,17}X)$/
  if (!reg.test(num)) {
    return false
  }
  // 校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
  // 下面分别分析出生日期和校验位
  const len = num.length
  let re = ''
  if (len === 15) {
    re = new RegExp(
      /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/)
    const arrSplit = num.match(re)
    // 检查生日日期是否正确
    dtmBirth = new Date('19' + arrSplit[2] + '/' + arrSplit[3] + '/' + arrSplit[4])
    const bGoodDay = (dtmBirth.getYear() === Number(arrSplit[2])) &&
      ((dtmBirth.getMonth() + 1) === Number(arrSplit[3])) &&
      (dtmBirth.getDate() === Number(arrSplit[4]))
    if (!bGoodDay) {
      return false
    }
  }
  if (len === 18) {
    re = new RegExp(/^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/)
    const arrSplit = num.match(re)
    // 检查生日日期是否正确
    dtmBirth = new Date(arrSplit[2] + '/' +
      arrSplit[3] + '/' + arrSplit[4])
    const bGoodDay = (dtmBirth.getFullYear() === Number(arrSplit[2])) &&
      ((dtmBirth.getMonth() + 1) === Number(arrSplit[3])) &&
      (dtmBirth.getDate() === Number(arrSplit[4]))
    if (!bGoodDay) {
      return false
    } else {
      // 检验18位身份证的校验码是否正确。
      // 校验位按照ISO 7064:1983.MOD
      // 11-2的规定生成，X可以认为是数字10。
      const arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
      const arrCh = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
      let nTemp = 0
      for (let i = 0; i < 17; i++) {
        nTemp += num.substr(i, 1) * arrInt[i]
      }
      const valnum = arrCh[nTemp % 11]
      if (valnum !== num.substr(17, 1)) {
        return false
      }
    }
  }
  return true
}

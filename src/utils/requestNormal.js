import axios from 'axios'
// import { getErrorMessageFilter } from 'baseModule@/config/baseConfig'
// import { Message } from 'element-ui'
import store from '@/store'
import retCode from './retCode'
import qs from 'qs'
import CryptoJS from 'crypto-js'
if (process.env.VUE_APP_HTTP_CYPTO === 'true' && process.env.NODE_ENV !== 'production') {
  console.log('%cHttp通信加密，localStorege增加log:network=true显示通信日志', 'color: #fff; background: #f40; font-size: 14px;')
}

const aesKey = 'SQxlea4xGJhqmppenxhw44OxEYy#bmxr'
// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 6000000 // request timeout
  // withCredentials: true
  // headers: {
  //   'Content-Type': 'application/json;charset=utf-8'
  // }
})
var message = ''
service.interceptors.request.use(
  config => {
    // do something before request is sent
    if (!config.params) {
      config.params = {}
    }
    if (config.method === 'get') {
      delete config.headers['Content-Type']
      config.paramsSerializer = function(params) {
        return qs.stringify(params, { arrayFormat: 'repeat' })
      }
    }

    config.params['_'] = Math.random()
    if (config.data) {
      // 加密后数据都是字符串，需要手动指定解密后的格式
      // if(!config.headers['Content-Type']){
      if (typeof (config.data) === 'string') {
        config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
      } else if (config.data instanceof FormData) {
        config.headers['Content-Type'] = 'multipart/form-data'
      } else if (typeof (config.data) === 'object') {
        config.headers['Content-Type'] = 'application/json;charset=UTF-8'
      }
      // }

      // 处理body数据加密，multipart数据不加密
      if (process.env.VUE_APP_HTTP_CYPTO === 'true' && config.data && !(config.data instanceof FormData)) {
        config.headers['crypto'] = '1'
        const isJsonData = typeof (config.data) === 'object'
        const keyHex = CryptoJS.enc.Utf8.parse(aesKey)
        const encrypted = CryptoJS.AES.encrypt(isJsonData ? JSON.stringify(config.data) : config.data, keyHex, {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7
        })
        const enc = encrypted.ciphertext.toString(CryptoJS.enc.Base64)
        config.rawData = config.data
        config.data = enc
      }
    }
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    if (response.headers['web-version']) {
      if (+process.env.VUE_APP_VERSION_CODE < +response.headers['web-version']) {
        location.reload()
        return
      }
    }
    if (response.headers['crypto'] === '1') {
      const keyHex = CryptoJS.enc.Utf8.parse(aesKey)
      const decrypted = CryptoJS.AES.decrypt({
        ciphertext: CryptoJS.enc.Base64.parse(response.data.replace(new RegExp('"', 'g'), ''))
      }, keyHex, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      })
      response.data = JSON.parse(decrypted.toString(CryptoJS.enc.Utf8))
    }

    if (localStorage.getItem('log:network')) {
      console.log('network', response.config.url, JSON.parse(JSON.stringify(response)))
    }

    // console.log('返回数据->', res)
    if (response.config.rawResponseHandler) {
      return response.config.rawResponseHandler(response.data)
    }
    const res = response.data
    if (res.code !== retCode.RET_OK) {
      if (res.message !== message && !response.config.hideMessage) {
        // const errorMessageFilter = getErrorMessageFilter()
        // if (!errorMessageFilter || errorMessageFilter(response)) {
        //   Message({
        //     message: res.message || 'Error',
        //     type: 'error',
        //     duration: 5 * 1000,
        //     showClose: true,
        //     onClose: function() {
        //       message = ''
        //     }
        //   })
        // }
        message = res.message
      }

      if (res.code === retCode.RET_NOT_LOGIN) {
        // to re-login
        store.commit('user/RESET_STATE')
        store.dispatch('router/resetRouter')
        location.reload()
        return
      }
      if (res.code === retCode.TO_LOGIN) {
        store.dispatch('user/logout')
        return Promise.reject(res.message)
      }

      return Promise.reject(res)
    } else {
      return res.data
    }
  },
  error => {
    console.log('err' + error) // for debug
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000,
      showClose: true
    })
    return Promise.reject(error)
  }
)

export default service

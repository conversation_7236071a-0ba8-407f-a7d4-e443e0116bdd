/** 获取姓名的最后一个字 */
export function userNameComp(name) {
  if (name) {
    const lastCharIndex = name.length - 1;
    return name.charAt(lastCharIndex);
  }
  return "";
}
/** 加密 */
/** **宇 */
export function encipherName(name) {
  if (name) {
    const lastName = name.charAt(name.length - 1);
    const asterisks = "*".repeat(name.length - 1);
    return asterisks + lastName;
  }
  return "";
}
/** 187******79 */
export function encipherPhone(phone) {
  if (phone) {
    const maskedPhoneNumber = phone.replace(
      /^(\d{3})\d*(\d{2})$/,
      "$1******$2"
    );
    return maskedPhoneNumber;
  }
}

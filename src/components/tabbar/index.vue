<template>
  <nav class="tabbar">
    <div
      v-for="item in tabs"
      class="tabbar-item"
      :class="{ active: props.activePath === item.path }"
      :key="item.name"
      @click="go(item)"
    >
      <svg-icon icon-class="mine" />
      <span>{{ item.label }}</span>
    </div>
  </nav>
</template>

<script setup>
import { useRouter, useRoute } from "@/composition-helpers";
// 你可以根据实际需求自定义 tabs
const props = defineProps({
  activePath: {
    type: String,
    default: ""
  }
});
const router = useRouter();
const route = useRoute();
console.log("tabbar", route);
const tabs = [
  {
    name: "home",
    label: "首页",
    icon: "home",
    path: "/"
  },
  {
    name: "need",
    label: "供需对接",
    icon: "need",
    path: "/need"
  },
  {
    name: "activity",
    label: "财金联盟",
    icon: require("@/assets/images/tabbar/mine.svg"),
    path: "/activity"
  },
  {
    name: "mine",
    label: "我的",
    icon: require("@/assets/images/tabbar/mine.svg"),
    path: "/mine"
  }
];
const go = item => {
  router.push(item.path);
};
</script>

<style scoped lang="scss">
.tabbar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 56px;
  background: #fff;
  display: flex;
  border-top: 1px solid #eee;
  z-index: 100;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  color: #333;
  padding: 0 16px;
  box-shadow: 2 2px 2px 2px #000;
  border-radius: 10px 10px 0 0;
  // border: none;
}
.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: #888;
  font-size: 14px;
  padding-top: 6px;
  cursor: pointer;
  transition: color 0.2s;
  .tabbar-icon {
    font-size: 16px;
    height: 17px;
    width: 17px;
    margin-bottom: 6px;
  }
}
.tabbar-item .iconfont {
  display: block;
  font-size: 22px;
  margin-bottom: 2px;
}
.tabbar-item.active {
  color: #418dfd;
  font-weight: 500;
  .tabbar-icon {
    color: #418dfd;
  }
}
</style>

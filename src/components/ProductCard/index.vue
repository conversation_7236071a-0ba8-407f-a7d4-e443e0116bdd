<template>
  <div class="product-card">
    <!-- 产品标题和银行logo -->
    <div class="card-header">
      <div class="product-info">
        <h3 class="product-name">{{ product.cpmc }}</h3>
        <div class="bank-info">
          <span class="bank-logo">
            {{ product.sftycp }}
          </span>
        </div>
      </div>
    </div>

    <!-- 产品详情 -->
    <div class="card-content">
      <div class="product-details">
        <div class="detail-item">
          <div class="detail-value interest-rate">
            {{ product.cklvq }}% {{ "-" + product.cklvz }}%
          </div>
          <div class="detail-label">参考利率</div>
        </div>
        <div class="detail-item">
          <div class="detail-value">
            {{ product.dkedq }}{{ "-" + product.dkedz }}
          </div>
          <div class="detail-label">贷款额度(元)</div>
        </div>
        <div class="detail-item">
          <div class="detail-value">{{ product.dkzq }}个月</div>
          <div class="detail-label">贷款期限</div>
        </div>
      </div>

      <!-- 标签和按钮 -->
      <div class="card-footer">
        <div class="product-tags">
          <span
            v-for="(tag, index) in product.dbfs
              ? product.dbfs.split(';')
              : product.dbfs"
            :key="index"
            class="tag"
            :class="getTagClass(tag)"
          >
            {{ getGuaranteeTypeName(tag) }}
          </span>
        </div>
        <van-button
          type="primary"
          size="small"
          round
          class="apply-btn"
          @click="handleApply"
        >
          我要贷款
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProductCard",
  props: {
    product: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  computed: {
    bankLogoUrl() {
      // 根据银行名称返回对应的logo
      const logoMap = {
        农业发展银行: "/placeholder.svg?height=24&width=24&text=农发",
        中国农业银行: "/placeholder.svg?height=24&width=24&text=农行"
      };
      return (
        logoMap[this.product.bankName] || "/placeholder.svg?height=24&width=24"
      );
    }
  },
  methods: {
    handleApply() {
      this.$emit("apply", this.product);
    },
    getTagClass(tag) {
      const tagClassMap = {
        "01": "tag-mortgage",
        "02": "tag-pledge",
        "03": "tag-credit",
        "04": "tag-fund",
        "05": "tag-guarantee",
        "06": "tag-joint-guarantee",
        // 保留原有的文本映射
        担保贷款: "tag-guarantee",
        信用贷款: "tag-credit",
        扶持农业: "tag-agriculture"
      };
      return tagClassMap[tag] || "tag-default";
    },
    getGuaranteeTypeName(tag) {
      const guaranteeTypeMap = {
        "01": "抵押",
        "02": "质押",
        "03": "信用",
        "04": "信保基金",
        "05": "一般保证",
        "06": "连带责任保证",
        担保贷款: "担保贷款",
        信用贷款: "信用贷款",
        扶持农业: "扶持农业"
      };
      return guaranteeTypeMap[tag] || tag;
    }
  }
};
</script>

<style lang="scss" scoped>
.product-card {
  background-color: white;
  border-radius: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  .card-header {
    padding: 16px 16px 0;

    .product-info {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .product-name {
        font-size: 18px;
        font-weight: 600;
        color: #323233;
        margin: 0;
      }

      .bank-info {
        .bank-logo {
          width: 24px;
          height: 24px;
          border-radius: 4px;
        }
        font-size: 14px;
      }
    }
  }

  .card-content {
    padding: 16px;

    .product-details {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;

      .detail-item {
        flex: 1;
        text-align: left;

        .detail-value {
          font-size: 16px;
          font-weight: 600;
          color: #323233;
          margin-bottom: 4px;
          text-wrap: nowrap;
          text-align: center;

          &.interest-rate {
            color: #ee0a24;
            font-size: 18px;
          }
        }

        .detail-label {
          font-size: 12px;
          color: #969799;
          text-align: center;
        }
      }
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #eff3ff;

      .product-tags {
        display: flex;
        // gap: 28px;

        .tag {
          padding: 1px;
          border-radius: 12px;
          font-size: 14px;
          text-wrap: nowrap;
          color: #3f7ffc;
          //   border: 1px solid;

          // &.tag-mortgage {
          //   color: #1989fa;
          //   background-color: #e8f3ff;
          // }

          // &.tag-pledge {
          //   color: #ff6b35;
          //   // background-color: #fff2ed;
          // }

          // &.tag-credit {
          //   color: #00c853;
          //   //  background-color: #e8f8f0;
          // }

          // &.tag-fund {
          //   color: #9c27b0;
          //   // background-color: #f3e5f5;
          // }

          // &.tag-guarantee {
          //   color: #ff9800;
          //   // background-color: #fff3e0;
          // }

          // &.tag-joint-guarantee {
          //   color: #f44336;
          //   // background-color: #ffebee;
          // }

          // &.tag-agriculture {
          //   color: #4caf50;
          //   //  background-color: #e8f5e8;
          // }

          // &.tag-default {
          //   color: #646566;
          //   background-color: #f7f8fa;
          // }
        }
      }

      .apply-btn {
        min-width: 80px;
        height: 32px;
        border-radius: 8px;

        :deep(.van-button__text) {
          font-size: 13px;
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .product-card {
    .card-content {
      .product-details {
        .detail-item {
          .detail-value {
            font-size: 14px;

            &.interest-rate {
              font-size: 16px;
            }
          }
        }
      }

      .card-footer {
        .product-tags {
          .tag {
            font-size: 11px;
            padding: 3px 6px;
          }
        }

        .apply-btn {
          min-width: 70px;
          height: 28px;

          :deep(.van-button__text) {
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>

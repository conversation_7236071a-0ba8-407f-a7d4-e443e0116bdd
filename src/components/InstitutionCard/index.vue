<template>
  <div class="institution-card" @click="handleClick">
    <div class="card-content">
      <!-- 机构信息 -->
      <div class="institution-info">
        <div class="institution-main">
          <h3 class="institution-name">
            {{ institution.jgmcQc || institution.jgmc }}
            <div class="institution-logo">
              <img :src="institution.logolj" />
            </div>
          </h3>
          <div class="institution-meta">
            <!-- <span class="product-tag" v-if="institution.hasProducts">
              <van-icon name="shop-o" />
              发布融资产品
            </span> -->
            <div class="left">
              <img src="@/assets/images/orglist/pro_num.png" alt="" />
              发布产品
            </div>
            <span class="product-count">{{ institution.num }}个</span>
          </div>
        </div>

        <!-- 机构logo -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "InstitutionCard",
  props: {
    institution: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  methods: {
    handleClick() {
      this.$emit("click", this.institution);
    }
  }
};
</script>

<style lang="scss" scoped>
.institution-card {
  background-color: white;
  border-radius: 8px;
  margin-bottom: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    background-color: #f8f9fa;
  }

  .card-content {
    padding: 16px;

    .institution-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .institution-main {
        flex: 1;
        min-width: 0;

        .institution-name {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 15px;
          font-weight: 500;
          color: #323233;
          margin: 0 0 16px 0;
          line-height: 1.4;
          word-break: break-all;
        }

        .institution-meta {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          // gap: 12px;
          img {
            height: 14px;
            width: 14px;
          }
          .product-tag {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
            // color: #00c853;

            .van-icon {
              font-size: 12px;
            }
          }

          .product-count {
            font-size: 18px;
            color: #ee0a24;
            font-weight: 500;
          }
        }
      }

      .institution-logo {
        flex-shrink: 0;

        img {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          object-fit: cover;
        }
        font-size: 14px;
      }
    }
  }
}
</style>

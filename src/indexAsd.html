<!DOCTYPE html>
<html lang="en">
  <head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta
      content="width=device-width,initial-scale=1,user-scalable=no"
      name="viewport"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="format-detection" content="telephone=no,address=no" />
    <meta name="apple-mobile-web-app-status-bar-style" content="white" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <link rel="icon" href="./favicon.ico" />
    <title></title>

    <script
      type="text/javascript"
      src="https://cdn.bootcdn.net/ajax/libs/eruda/2.4.1/eruda.js"
    ></script>
    <script
      type="text/javascript"
      src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"
    ></script>
    <script type="text/javascript" src="sm2/build/SM.js"></script>
    <script type="text/javascript" src="SM.js"></script>
    <script src="asdConfig.js"></script>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but vue2-project-plugin doesn't work properly without
        JavaScript enabled. Please enable it to continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->

    <script>
      window.eruda = eruda;
      window.onload = function() {
        if (window.SM) {
          console.log("SM is ready");
          window.eventBus.$emit("asdReady", { jssdkconfig, lightAppJssdk });
        }
      };
    </script>
  </body>
</html>

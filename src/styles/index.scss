
@import './mixin.scss';
@import './utils.scss';

body {
    overflow-x: hidden;
    overflow-y: auto;
    overflow: overlay;
    height: 100%;
    margin: 0 auto;
    padding: 0;
    background: #F6F6F6;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
    font-weight: 700;
}

html {
    height: 100%;
    box-sizing: border-box;
}

#app {
    min-height: 100%;
    background: #F6F6F6;
}

* {
    box-sizing: border-box;
}

*:before,
*:after {
    box-sizing: inherit;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
}

div:focus {
    outline: none;
}
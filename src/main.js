import Vue from "vue";
import App from "./App.vue";
import "amfe-flexible";
import Vant, { Notify } from "vant";

import "vant/lib/index.css";
import pinia from "./store";
import router from "./router";
import "normalize.css/normalize.css";
import "@/styles/index.scss";
import "@/permission";
import "@/icons"; // icon
Vue.config.productionTip = false;
// window.eventBus.$on("asdReady", res => {
//   console.log("====爱山东注册成功=====");
//   const { Asd } = require("./utils/asd");
//   Asd.init(res);
// });
if (process.env.NODE_ENV !== "production") {
  window.eruda.init();
}
// 全局注册
Vue.use(Notify);
Vue.use(Vant);
new Vue({
  el: "#app",
  pinia,
  router,
  render: h => h(App)
});

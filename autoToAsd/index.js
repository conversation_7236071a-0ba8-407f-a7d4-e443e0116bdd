/**
import { TOKEN } from '../config';
 * 自动化上传爱山东
 */
const judgeToken = require("./http");
const loginWithPuppeteer = require("./login");

async function autoToAsd() {
  let isExpired = false;
  /**  1 、首先调用个接口看下token 是否过期 */
  isExpired = await judgeToken().catch(() => {
    return true;
  });
  console.log("isExpired", isExpired);
  /**
   * 2 、如果过期 使用 puppeteer 模拟登录 获取新的 token
   *    2.1 需要手动输入下验证码（可以改为百度云ocr识别，待做）
   * */
  if (isExpired) {
    await loginWithPuppeteer();
    autoToAsd();
  } else {
    console.log("token 未过期");
  }
  /**  3 、调用上传新版本包接口 */
}
autoToAsd();
module.exports = autoToAsd;

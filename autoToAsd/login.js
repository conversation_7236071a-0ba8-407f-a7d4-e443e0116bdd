const puppeteer = require("puppeteer");
const readline = require("readline");
const fs = require("fs");
const path = require("path");
const { USERNAME, PASSWORD, LOGIN_URL, SELECTORS } = require("./config.js");
async function loginWithPuppeteer() {
  console.log("使用 puppeteer 模拟登录中...");
  const browser = await puppeteer.launch({
    headless: false,
    executablePath:
      "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
  }); // 显示浏览器，方便输入验证码
  const page = await browser.newPage();
  await page.goto(LOGIN_URL, { waitUntil: "networkidle2" });

  // 输入用户名和密码
  await page.click(SELECTORS.username);
  await page.$eval(SELECTORS.username, el => (el.value = ""));
  await page.waitForSelector(SELECTORS.username);
  await page.type(SELECTORS.username, USERNAME);
  await page.type(SELECTORS.password, PASSWORD);

  // 等待验证码图片加载
  await page.waitForSelector(SELECTORS.captchaImg);

  // 截图验证码图片，保存到本地
  const captchaImg = await page.$(SELECTORS.captchaImg);
  await captchaImg.screenshot({ path: "captcha.jpg" });
  console.log("验证码图片已保存为 captcha.jpg");

  // 命令行输入验证码
  const captcha = await getUserInput("请输入验证码图片内容: ");
  await page.click(SELECTORS.captchaInput);
  await page.$eval(SELECTORS.captchaInput, el => (el.value = ""));
  await page.type(SELECTORS.captchaInput, captcha);

  // 点击登录按钮
  await page.click(SELECTORS.loginBtn);

  // 等待页面跳转或登录成功
  await page.waitForTimeout(3000); // 可根据实际情况调整等待时间

  // 获取登录后的 cookies
  const cookies = await page.cookies();
  console.log("登录后的 cookies:", cookies);

  // 获取 localStorage 里的 token
  let token = await page.evaluate(() =>
    localStorage.getItem("jpaas_access_token")
  );
  token = typeof token === "string" ? JSON.parse(token) : token;
  console.log("登录后的 token:", token);

  // 写入 src/constants/index.js
  const tokenFile = path.join(__dirname, "./config.js");
  let content = "";
  if (fs.existsSync(tokenFile)) {
    content = fs.readFileSync(tokenFile, "utf-8");
    // 替换 TOKEN 的值
    if (/const TOKEN = ".*";/.test(content)) {
      content = content.replace(
        /const TOKEN = ".*";/,
        `const TOKEN = "${token.value}";`
      );
    } else {
      content = `const TOKEN = "${token.value}";\n` + content;
    }
  } else {
    content = `const TOKEN = "${token.value}";\n`;
  }
  fs.writeFileSync(tokenFile, content, "utf-8");
  console.log("TOKEN写入 config 文件成功,即将执行上传 dist...");

  await browser.close();
}
function getUserInput(prompt) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  return new Promise(resolve =>
    rl.question(prompt, answer => {
      rl.close();
      resolve(answer);
    })
  );
}
module.exports = loginWithPuppeteer;

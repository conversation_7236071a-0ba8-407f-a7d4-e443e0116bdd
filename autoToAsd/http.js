/** 测试下 token 是否过期 */
const axios = require("axios");
const { TOKEN } = require("./config");
console.log(TOKEN);
async function judgeToken() {
  console.log(1);
  const url =
    "https://fw1.isdapp.shandong.gov.cn/api-gateway/jpaas-appcenter-server/manager/back/app/list";

  return axios
    .post(
      url,
      {
        pageNo: 1,
        pageSize: 10,
        type: 1
      },
      {
        headers: {
          "Access-token": TOKEN,
          "Content-Type": "application/json"
        }
      }
    )
    .then(() => {
      return false;
    })
    .catch(error => {
      if (error.response && error.response.status === 401) {
        console.log("token 过期");
        /** 去重新登录 */
        return true;
      }
    });
}
module.exports = judgeToken;

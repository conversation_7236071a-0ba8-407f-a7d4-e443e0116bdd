const puppeteer = require("puppeteer");

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    executablePath:
      "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", // 可选，指定你的 Chrome
    userDataDir: "~/Library/Application Support/Google/Chrome" // 可选，使用你的个人数据
  });
  const page = await browser.newPage();
  await page.goto("https://fw1.isdapp.shandong.gov.cn/jpaas/workbench");

  // 等待页面加载完成（可根据实际情况调整）
  await page.waitForTimeout(2000);

  // 获取 localStorage 的所有内容
  const localStorageData = await page.evaluate(() => {
    const data = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      data[key] = localStorage.getItem(key);
    }
    return data;
  });

  console.log("localStorage:", localStorageData);

  await browser.close();
})();

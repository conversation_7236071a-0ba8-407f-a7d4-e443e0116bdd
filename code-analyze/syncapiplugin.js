const path = require("path"); // 导入 'path' 模块，用于处理文件路径
const fs = require("fs"); // 导入 'fs' 模块，用于文件系统操作
const request = require("request"); // 导入 'request' 模块，用于发送 HTTP 请求

// 尝试加载开发配置文件
var devConfig;
try {
  devConfig = require("../dev-config.js");
} catch (e) {
  console.log("开发配置文件加载失败:", e);
}

/**
 * SyncApiPlugin - Webpack 插件
 *
 * 功能说明：
 * 1. 收集项目中 Vue 组件对 API 模块的导入信息
 * 2. 收集 API 模块中导出的 URL 信息
 * 3. 分析模块间的依赖关系，生成 API 使用报告
 * 4. 将收集到的 API 信息发送到开发服务器进行同步
 */
class SyncApiPlugin {
  constructor() {
    // 存储模块导入信息的对象
    // 格式: { 'views/home/<USER>': [{ identifierName: 'fetchData', sourceModuleName: 'api/user' }] }
    this.moduleImportList = {};

    // 存储模块导出 URL 的对象
    // 格式: { 'api/user': { 'fetchData': 'api/user/list' } }
    this.moduleExportUrlList = {};
  }

  /**
   * 递归收集指定模块及其依赖模块的 API URL
   *
   * @param {string} moduleName - 要收集的模块名称
   * @param {Array} apiList - 存储收集到的 API URL 的数组
   * @param {Array} processedModule - 已处理模块的列表，防止循环依赖
   */
  collectApi(moduleName, apiList, processedModule) {
    // 防止循环依赖，如果模块已经处理过则直接返回
    if (processedModule.indexOf(moduleName) > -1) {
      return;
    }

    // 标记当前模块为已处理
    processedModule.push(moduleName);

    // 获取当前模块的导入列表
    const importList = this.moduleImportList[moduleName];
    if (!importList) {
      return;
    }
    console.log(
      "this.moduleImportList ------",
      this.moduleImportList,
      this.moduleExportUrlList
    );
    // 遍历每个导入信息
    for (const importInfo of importList) {
      // 检查导入是否为 API 模块引用
      if (importInfo.sourceModuleName.startsWith("api/")) {
        // 获取源 API 模块的 URL 列表
        const urlList = this.moduleExportUrlList[importInfo.sourceModuleName];
        if (urlList) {
          // 根据标识符名称获取对应的 API URL
          const apiUrl = urlList[importInfo.identifierName];
          // 如果 URL 存在且不在列表中，则添加到列表
          if (apiUrl && apiList.indexOf(apiUrl) === -1) {
            apiList.push(apiUrl);
          }
        }
      } else {
        // 如果不是 API 模块，递归收集该模块的 API
        this.collectApi(importInfo.sourceModuleName, apiList, processedModule);
      }
    }
  }

  /**
   * 将插件应用于 Webpack 编译器
   *
   * @param {Object} compiler - Webpack 编译器实例
   */
  apply(compiler) {
    // ==================== 模块解析阶段 ====================

    /**
     * 监听模块工厂，用于处理模块的创建和解析
     */
    compiler.hooks.normalModuleFactory.tap("SyncApiPlugin", factory => {
      /**
       * 监听 JavaScript 解析器，用于分析模块的导入和导出
       */
      factory.hooks.parser
        .for("javascript/auto")
        .tap("SyncApiPlugin", (parser, options) => {
          /**
           * 监听导入语句，收集 Vue 组件对 API 模块的导入信息
           *
           * @param {Object} statement - 导入语句的 AST 节点
           * @param {string} source - 导入的模块路径
           * @param {string} exportName - 导出的名称
           * @param {string} identifierName - 导入时使用的标识符名称
           */
          parser.hooks.importSpecifier.tap(
            "SyncApiPlugin",
            (statement, source, exportName, identifierName) => {
              const file = parser.state.current.resource;

              // 只处理 Vue 组件的 script 部分
              if (
                !file ||
                file.indexOf(".vue?vue&type=script&lang=js") === -1
              ) {
                return;
              }

              let sourceModuleName = "";
              // debugger;
              // 处理相对路径导入 (如: './api/user')
              if (source.startsWith(".")) {
                const sourceFile = path.join(
                  parser.state.current.context,
                  source
                );
                sourceModuleName = sourceFile
                  .replace(/\//g, "\\")
                  .split("src\\")[1]
                  .split(".vue")[0]
                  .split(".js")[0]
                  .replace(/\\/g, "/");
              } else if (source.startsWith("@/")) {
                // 处理别名路径导入 (如: '@/api/user')
                sourceModuleName = source
                  .replace(/@\//g, "")
                  .split(".vue")[0]
                  .split(".js")[0]
                  .replace(/\\/g, "/");
              } else {
                // 忽略其他类型的导入
                return;
              }

              // 提取当前模块名称
              const moduleName = file
                .replace(/\//g, "\\")
                .split("src\\")[1]
                .split(".vue")[0]
                .replace(/\\/g, "/");

              // 获取或创建当前模块的引用列表
              let refList = this.moduleImportList[moduleName];
              if (!refList) {
                refList = [];
                this.moduleImportList[moduleName] = refList;
              }
              console.log(
                "file",
                file,
                "source",
                source,
                "moduleName",
                moduleName,
                this.moduleImportList
              );
              // 添加导入信息到引用列表
              refList.push({
                identifierName: exportName, // 导入时使用的名称
                sourceModuleName: sourceModuleName // 源模块名称
              });
            }
          );

          /**
           * 监听导出语句，收集 API 模块中导出的 URL 信息
           *
           * @param {Object} statement - 导出语句的 AST 节点
           * @param {string} identifierName - 标识符名称
           * @param {string} exportName - 导出的名称
           * @param {number} index - 导出索引
           */
          parser.hooks.exportSpecifier.tap(
            "SyncApiPlugin",
            (statement, identifierName, exportName, index) => {
              const file = parser.state.current.resource;

              // 只处理 src/api 目录下的文件
              if (
                !file ||
                file.replace(/\//g, "\\").indexOf("\\src\\api\\") === -1
              ) {
                return;
              }

              // 提取模块名称
              const moduleName = file
                .replace(/\//g, "\\")
                .split("src\\")[1]
                .split(".js")[0]
                .replace(/\\/g, "/");

              try {
                // 解析导出语句中的属性
                const properties =
                  statement.declaration.body.body[0].argument.arguments[0]
                    .properties;
                if (!properties) {
                  return;
                }
                console.log("properties ---->", properties);
                // 遍历每个属性
                for (const property of properties) {
                  const key = property.key.name;
                  let value = property.value;

                  // 只处理 url 属性
                  if (key === "url") {
                    // 根据不同的 AST 节点类型提取 URL 值
                    if (value.type === "CallExpression") {
                      // 处理函数调用表达式
                      value = value.callee.object.callee.object.value;
                    } else if (value.type === "BinaryExpression") {
                      // 处理二元表达式
                      value = value.left.value;
                    } else {
                      // 处理字面量
                      value = value.value;
                    }

                    // 清理 URL 格式
                    if (value.startsWith("/")) {
                      value = value.replace("/", "");
                    }
                    // 移除查询参数
                    value = value.split("?")[0];

                    // 获取或创建模块的导出 URL 列表
                    let exportUrlList = this.moduleExportUrlList[moduleName];
                    if (!exportUrlList) {
                      exportUrlList = {};
                      this.moduleExportUrlList[moduleName] = exportUrlList;
                    }

                    // 存储 URL 信息
                    exportUrlList[exportName] = value;
                  }
                }
              } catch (e) {
                console.log(
                  `导出解析错误 - 模块: ${moduleName}, 导出: ${exportName}, 标识符: ${identifierName}`
                );
              }
            }
          );
        });
    });

    // ==================== 编译完成阶段 ====================

    /**
     * 监听编译完成事件，进行 API 信息收集和同步
     */
    compiler.hooks.done.tap("SyncApiPlugin", compilation => {
      console.log("开始收集 API 信息...");

      // 存储每个模块收集的 API 列表
      const moduleApiList = {};
      console.log("this.moduleImportList", this.moduleImportList);
      // 遍历所有模块，收集 API 信息
      for (const moduleName in this.moduleImportList) {
        moduleApiList[moduleName] = [];
        const processedModule = [];
        this.collectApi(moduleName, moduleApiList[moduleName], processedModule);
      }

      // 将收集的 API 列表写入文件
      fs.writeFile(
        "./apiList.txt",
        JSON.stringify(moduleApiList, null, 2),
        err => {
          if (err) {
            console.error("写入 API 列表文件失败:", err);
          } else {
            console.log("API 列表已写入 apiList.txt");
          }
        }
      );

      // 如果配置了开发服务器，发送 API 信息进行同步
      // if (devConfig && devConfig.devServer) {
      //   console.log("正在发送 API 信息到服务器:", devConfig.devServer);

      //   request.post(
      //     {
      //       url: devConfig.devServer + "api/menu/import-function",
      //       json: moduleApiList
      //     },
      //     (error, response, body) => {
      //       if (error) {
      //         console.error("发送 API 信息失败:", error);
      //         return;
      //       }

      //       if (response.statusCode === 200) {
      //         const add = body.data.add || {};
      //         const del = body.data.del || {};

      //         // 输出建议删除的 API
      //         for (const key in del) {
      //           const delApiList = del[key];
      //           console.log(
      //             `建议删除文件: ${key} - ${JSON.stringify(delApiList)}`
      //           );
      //         }
      //         console.log(`建议删除总数: ${Object.keys(del).length}`);

      //         // 输出新增的 API
      //         for (const key in add) {
      //           const addApiList = add[key];
      //           console.log(`新增 API: ${key} - ${JSON.stringify(addApiList)}`);
      //         }
      //         console.log(`新增总数: ${Object.keys(add).length}`);
      //       } else {
      //         console.error("服务器响应错误:", response.statusCode, body);
      //       }
      //     }
      //   );
      // } else {
      //   console.log("未配置开发服务器，跳过 API 同步");
      // }
    });
  }
}

module.exports = SyncApiPlugin;

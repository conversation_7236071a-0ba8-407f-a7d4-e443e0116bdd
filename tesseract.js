const Tesseract = require("tesseract.js");

async function recognizeCaptchaByTesseract(imagePath) {
  const {
    data: { text }
  } = await Tesseract.recognize(
    "./captcha.jpg",
    "eng", // 英文数字验证码用 eng
    {
      logger: m => console.log(m) // 可选，输出识别进度
    }
  );
  // 去除空格和换行
  return text.replace(/\s/g, "");
}

// 用法示例
(async () => {
  const captcha = await recognizeCaptchaByTesseract("captcha.jpg");
  console.log("tesseract.js 识别结果:", captcha);
})();
